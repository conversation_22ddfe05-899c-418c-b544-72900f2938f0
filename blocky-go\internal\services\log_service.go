package services

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	
	"blocky-go/internal/database"
	"blocky-go/internal/models"
)

// LogService handles device log operations
type LogService struct {
	db      *database.DB
	queries *models.Queries
}

// NewLogService creates a new log service
func NewLogService(db *database.DB) *LogService {
	return &LogService{
		db:      db,
		queries: models.New(db.Pool),
	}
}

// LogEntry represents a device log entry
type LogEntry struct {
	DeviceUUID string    `json:"uuid" validate:"required"`
	Timestamp  time.Time `json:"timestamp"`
	LogType    string    `json:"logType" validate:"required,oneof=message warning error"`
	Message    string    `json:"message" validate:"required"`
}

// StoreLogData stores device log data
func (s *LogService) StoreLogData(ctx context.Context, log LogEntry) error {
	// Set timestamp if not provided
	if log.Timestamp.IsZero() {
		log.Timestamp = time.Now()
	}
	
	// Validate log type
	if log.LogType != "message" && log.LogType != "warning" && log.LogType != "error" {
		return fmt.Errorf("invalid log type: %s", log.LogType)
	}
	
	// Store in database
	return s.queries.InsertDeviceLog(ctx, models.InsertDeviceLogParams{
		DeviceUuid: log.DeviceUUID,
		Timestamp:  pgtype.Timestamp{Time: log.Timestamp, Valid: true},
		LogType:    log.LogType,
		Message:    log.Message,
	})
}

// GetDeviceLogs retrieves logs for a specific device
func (s *LogService) GetDeviceLogs(ctx context.Context, uuid string, logType *string, startTime, endTime time.Time, limit, offset int) ([]models.DeviceLogs, error) {
	params := models.GetDeviceLogsParams{
		DeviceUuid: uuid,
		Timestamp:  pgtype.Timestamp{Time: startTime, Valid: true},
		Timestamp_2: pgtype.Timestamp{Time: endTime, Valid: true},
		Limit:      int32(limit),
		Offset:     int32(offset),
		Column2:    "", // Default to empty string
	}
	
	if logType != nil && *logType != "" {
		params.Column2 = *logType
	}
	
	return s.queries.GetDeviceLogs(ctx, params)
}