// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package models

import (
	"context"
)

type Querier interface {
	DeleteBME280Data(ctx context.Context, deviceUuid string) error
	DeleteDeviceLogs(ctx context.Context, deviceUuid string) error
	DeleteGPSData(ctx context.Context, deviceUuid string) error
	DeleteLTR390Data(ctx context.Context, deviceUuid string) error
	DeleteMHZ19BData(ctx context.Context, deviceUuid string) error
	DeletePMS7003Data(ctx context.Context, deviceUuid string) error
	DeleteWindData(ctx context.Context, deviceUuid string) error
	GetBME280History(ctx context.Context, arg GetBME280HistoryParams) ([]Bme280Data, error)
	GetDeviceLogs(ctx context.Context, arg GetDeviceLogsParams) ([]DeviceLogs, error)
	GetDeviceSummary(ctx context.Context) ([]GetDeviceSummaryRow, error)
	GetDeviceUUIDs(ctx context.Context) ([]GetDeviceUUIDsRow, error)
	GetGPSHistory(ctx context.Context, arg GetGPSHistoryParams) ([]GpsData, error)
	GetLTR390History(ctx context.Context, arg GetLTR390HistoryParams) ([]Ltr390Data, error)
	GetLatestBME280Data(ctx context.Context, deviceUuid string) (Bme280Data, error)
	GetLatestGPSData(ctx context.Context, deviceUuid string) (GpsData, error)
	GetLatestLTR390Data(ctx context.Context, deviceUuid string) (Ltr390Data, error)
	GetLatestMHZ19BData(ctx context.Context, deviceUuid string) (Mhz19bData, error)
	GetLatestPMS7003Data(ctx context.Context, deviceUuid string) (Pms7003Data, error)
	GetLatestWindData(ctx context.Context, deviceUuid string) (WindData, error)
	GetMHZ19BHistory(ctx context.Context, arg GetMHZ19BHistoryParams) ([]Mhz19bData, error)
	GetPMS7003History(ctx context.Context, arg GetPMS7003HistoryParams) ([]Pms7003Data, error)
	GetWindHistory(ctx context.Context, arg GetWindHistoryParams) ([]WindData, error)
	InsertBME280Data(ctx context.Context, arg InsertBME280DataParams) error
	InsertDeviceLog(ctx context.Context, arg InsertDeviceLogParams) error
	InsertGPSData(ctx context.Context, arg InsertGPSDataParams) error
	InsertLTR390Data(ctx context.Context, arg InsertLTR390DataParams) error
	InsertMHZ19BData(ctx context.Context, arg InsertMHZ19BDataParams) error
	InsertPMS7003Data(ctx context.Context, arg InsertPMS7003DataParams) error
	InsertWindData(ctx context.Context, arg InsertWindDataParams) error
}

var _ Querier = (*Queries)(nil)
