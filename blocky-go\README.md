# Blocky Go Backend

High-performance Go backend implementation providing 1:1 REST API compatibility with the existing Node.js backend. Benchmarks show 94-122x performance improvement over the Node.js implementation.

## Tech Stack

- **Framework**: Echo v4
- **Database**: PostgreSQL with pgx v5 driver
- **Query Builder**: sqlc for type-safe SQL
- **Migration**: golang-migrate
- **Docker**: Multi-stage build for minimal image size

## Architecture

This Go backend maintains exact API compatibility with the existing Node.js backend:

- Same REST endpoints and HTTP methods
- Identical JSON request/response formats  
- Compatible query parameters and pagination
- Matching error responses and status codes
- Same database schema and data types

## Quick Start with Docker

```bash
# Clone the repository
git clone <repository-url>
cd blocky-go

# Copy environment variables
cp .env.example .env

# Start the backend with PostgreSQL
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

The API will be available at `http://localhost:8086`

## Development Setup

### Prerequisites
- Go 1.23+
- PostgreSQL 16+
- Make

### Local Development

```bash
# Install dependencies
go mod download

# Install development tools
make install-tools

# Copy and configure environment
cp .env.example .env
# Edit .env with your database credentials

# Run database migrations
make migrate-up

# Generate sqlc models
make sqlc-generate

# Run development server with hot reload
make dev

# Run tests
make test

# Run benchmarks
go test -bench=. ./internal/handlers
```

## API Endpoints

### Sensor Data
- `POST /api/grouped/data` - Store grouped sensor data from ESP32
- `GET /api/grouped/latest?uuid=<uuid>` - Get latest grouped sensor data 
- `GET /api/grouped/devices/summary` - Get devices summary with statistics
- `GET /api/grouped/devices/basic` - Get lightweight devices info for polling
- `GET /api/grouped/devices/:uuid/history` - Get unified historical data
- `DELETE /api/grouped/data/:uuid` - Delete device data from all tables

### Logging
- `POST /api/logs/data` - Store log data from ESP32
- `GET /api/logs/device/:uuid` - Get log data for specific device

### System
- `GET /health` - Health check endpoint
- `GET /` - Root endpoint with API information

## Configuration

### Environment Variables

```bash
# PostgreSQL Configuration (for Docker Compose)
POSTGRES_USER=blocky          # Database user
POSTGRES_PASSWORD=blocky123   # Database password
POSTGRES_DB=blocky           # Database name
POSTGRES_PORT=5432           # Database port

# API Configuration
API_PORT=8086                # API server port
CORS_ORIGINS=http://localhost:5173,http://localhost:8085  # Allowed CORS origins
LOG_LEVEL=info               # Logging level (debug, info, warn, error)

# Direct Database URL (for local development)
DATABASE_URL=postgres://blocky:blocky123@localhost:5432/blocky?sslmode=disable
PORT=8086                    # Server port
```

## Docker Deployment

### Basic Usage

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f blocky-api

# Stop services
docker-compose down

# Stop and remove volumes (clean database)
docker-compose down -v
```

### With pgAdmin (Database Management)

```bash
# Start with pgAdmin included
docker-compose --profile tools up -d

# Access pgAdmin at http://localhost:5050
# Default credentials: <EMAIL> / admin123
```

### Production Deployment

```bash
# Build production image
docker build -t blocky-go:latest .

# Run with production settings
docker run -d \
  --name blocky-api \
  -p 8086:8086 \
  -e DATABASE_URL="*********************************/blocky?sslmode=require" \
  -e CORS_ORIGINS="https://your-frontend.com" \
  -e LOG_LEVEL="warn" \
  blocky-go:latest
```

## Performance

Benchmark results comparing Go vs Node.js implementations:

| Endpoint | Go Latency | Node.js Latency | Improvement |
|----------|------------|-----------------|-------------|
| POST /api/grouped/data | 6.78 μs | 642 μs | 94.6x faster |
| GET /api/grouped/latest | 5.05 μs | 493 μs | 97.7x faster |
| POST /api/logs/data | 4.54 μs | 555 μs | 122.3x faster |

## Database Schema

The backend uses the following PostgreSQL tables:
- `bme280_data` - Temperature, humidity, pressure sensor data
- `pms7003_data` - Particulate matter sensor data
- `mhz19b_data` - CO2 sensor data
- `gps_data` - GPS location data
- `ltr390_data` - UV index sensor data
- `wind_data` - Wind speed sensor data
- `device_logs` - Device log messages

All tables use device UUID for identification and include timestamps for historical tracking.