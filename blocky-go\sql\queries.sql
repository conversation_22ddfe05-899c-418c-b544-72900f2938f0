-- name: InsertBME280Data :exec
INSERT INTO bme280_data (device_uuid, timestamp, temperature, humidity, pressure)
VALUES ($1, $2, $3, $4, $5);

-- name: InsertPMS7003Data :exec
INSERT INTO pms7003_data (device_uuid, timestamp, pm01, pm25, pm10, n0p3, n0p5, n1p0, n2p5, n5p0, n10p0)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11);

-- name: InsertMHZ19BData :exec
INSERT INTO mhz19b_data (device_uuid, timestamp, co2, min_co2, temperature, accuracy)
VALUES ($1, $2, $3, $4, $5, $6);

-- name: InsertGPSData :exec
INSERT INTO gps_data (device_uuid, timestamp, latitude, longitude, altitude, quality, satellites, accuracy)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8);

-- name: InsertLTR390Data :exec
INSERT INTO ltr390_data (device_uuid, timestamp, uvi, lux)
VALUES ($1, $2, $3, $4);

-- name: InsertWindData :exec
INSERT INTO wind_data (device_uuid, timestamp, speed)
VALUES ($1, $2, $3);

-- name: InsertDeviceLog :exec
INSERT INTO device_logs (device_uuid, timestamp, log_type, message)
VALUES ($1, $2, $3, $4);

-- name: GetLatestBME280Data :one
SELECT * FROM bme280_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetLatestPMS7003Data :one
SELECT * FROM pms7003_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetLatestMHZ19BData :one
SELECT * FROM mhz19b_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetLatestGPSData :one
SELECT * FROM gps_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetLatestLTR390Data :one
SELECT * FROM ltr390_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetLatestWindData :one
SELECT * FROM wind_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1;

-- name: GetDeviceUUIDs :many
SELECT DISTINCT device_uuid, MAX(timestamp) as last_seen
FROM (
    SELECT device_uuid, timestamp FROM bme280_data
    UNION ALL
    SELECT device_uuid, timestamp FROM pms7003_data
    UNION ALL
    SELECT device_uuid, timestamp FROM mhz19b_data
    UNION ALL
    SELECT device_uuid, timestamp FROM gps_data
    UNION ALL
    SELECT device_uuid, timestamp FROM ltr390_data
    UNION ALL
    SELECT device_uuid, timestamp FROM wind_data
) AS all_data
GROUP BY device_uuid
ORDER BY last_seen DESC;

-- name: GetDeviceSummary :many
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'bme280' as sensor_type
FROM bme280_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'pms7003' as sensor_type
FROM pms7003_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'mhz19b' as sensor_type
FROM mhz19b_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'gps' as sensor_type
FROM gps_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'ltr390' as sensor_type
FROM ltr390_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'wind' as sensor_type
FROM wind_data
GROUP BY device_uuid
ORDER BY device_uuid, sensor_type;

-- name: GetBME280History :many
SELECT * FROM bme280_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetPMS7003History :many
SELECT * FROM pms7003_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetMHZ19BHistory :many
SELECT * FROM mhz19b_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetGPSHistory :many
SELECT * FROM gps_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetLTR390History :many
SELECT * FROM ltr390_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetWindHistory :many
SELECT * FROM wind_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5;

-- name: GetDeviceLogs :many
SELECT * FROM device_logs
WHERE device_uuid = $1
  AND ($2 = '' OR log_type = $2)
  AND timestamp >= $3
  AND timestamp <= $4
ORDER BY timestamp DESC
LIMIT $5 OFFSET $6;

-- name: DeleteBME280Data :exec
DELETE FROM bme280_data WHERE device_uuid = $1;

-- name: DeletePMS7003Data :exec
DELETE FROM pms7003_data WHERE device_uuid = $1;

-- name: DeleteMHZ19BData :exec
DELETE FROM mhz19b_data WHERE device_uuid = $1;

-- name: DeleteGPSData :exec
DELETE FROM gps_data WHERE device_uuid = $1;

-- name: DeleteLTR390Data :exec
DELETE FROM ltr390_data WHERE device_uuid = $1;

-- name: DeleteWindData :exec
DELETE FROM wind_data WHERE device_uuid = $1;

-- name: DeleteDeviceLogs :exec
DELETE FROM device_logs WHERE device_uuid = $1;