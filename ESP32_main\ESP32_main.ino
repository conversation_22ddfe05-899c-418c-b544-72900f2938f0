// Library Installation from Library Manager
// 1 - MHZCO2
// 2 - EspSoftwareSerial 
// 3 - BME280 
// 4 - ArduinoJson
// 5 - LTR390
// 6 - FuGPS
// 7 - ezTime

// Include Library bt adding .zip file (Sketch -> Include Library -> Add .ZIP Library...)
// 1 - PMserial-1.2.0

#include "Arduino.h"
#include "MHZCO2.h"
#include "SoftwareSerial.h"
#include <PMserial.h>
#include <BME280I2C.h>
#include <Wire.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include "esp_mac.h"
#include <LTR390.h>
#include <FuGPS.h>
#include <ezTime.h>

// Arduino Serial config
EspSoftwareSerial::UART ARDUINO_SERIAL;

// MH-Z19B config
EspSoftwareSerial::UART MHZ19B_SERIAL;
MHZ19B MHZ19B;

// PMS7003 config
SerialPM pms(PMS7003, 26, 25);  // Sensor Type, RX, TX

// BME280 config
BME280I2C bme;
BME280::TempUnit tempUnit(BME280::TempUnit_Celsius);
BME280::PresUnit presUnit(BME280::PresUnit_Pa);

// LTR390 config
LTR390 ltr390(0x53);

// GPS config
EspSoftwareSerial::UART GPS_SERIAL;
FuGPS gps(GPS_SERIAL);
byte dtmDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xA7, 0x00, 0x91, 0x20, 0x00, 0xF2, 0x67};
byte gbsDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xDE, 0x00, 0x91, 0x20, 0x00, 0x29, 0x7A};
byte gllDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xCA, 0x00, 0x91, 0x20, 0x00, 0x15, 0x16};
byte gnsDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xB6, 0x00, 0x91, 0x20, 0x00, 0x01, 0xB2};
byte grsDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xCF, 0x00, 0x91, 0x20, 0x00, 0x1A, 0x2F};
byte gsaDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xC0, 0x00, 0x91, 0x20, 0x00, 0x0B, 0xE4};
byte gstDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xD4, 0x00, 0x91, 0x20, 0x00, 0x1F, 0x48};
byte gsvDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xC5, 0x00, 0x91, 0x20, 0x00, 0x10, 0xFD};
byte rlmDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0x01, 0x04, 0x91, 0x20, 0x00, 0x50, 0x39};
byte thsDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xE3, 0x00, 0x91, 0x20, 0x00, 0x2E, 0x93};
byte utcDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xD0, 0x06, 0x91, 0x20, 0x00, 0x21, 0x4C};
byte vlwDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xE8, 0x00, 0x91, 0x20, 0x00, 0x33, 0xAC};
byte vtgDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xB1, 0x00, 0x91, 0x20, 0x00, 0xFC, 0x99};
byte zdaDisable [] = {0xB5, 0x62, 0x06, 0x8A, 0x09, 0x00, 0x00, 0x01, 0x00, 0x00, 0xD9, 0x00, 0x91, 0x20, 0x00, 0x24, 0x61};

// Sensors status
bool bme280_status = false;
bool ltr390_status = false;
bool pms_updated = false;
bool gps_updated = false;
bool MHZ19B_updated = false;
bool bme_updated = false;
bool ltr390_updated = false;

// Global variables to store latest sensor values
float latest_bme_temp = NAN, latest_bme_hum = NAN, latest_bme_pres = NAN;
int latest_mhz19b_co2 = 0, latest_mhz19b_minco2 = 0, latest_mhz19b_temp = 0, latest_mhz19b_accuracy = 0;
uint16_t latest_pms_pm01 = 0, latest_pms_pm25 = 0, latest_pms_pm10 = 0;
uint16_t latest_pms_n0p3 = 0, latest_pms_n0p5 = 0, latest_pms_n1p0 = 0, latest_pms_n2p5 = 0, latest_pms_n5p0 = 0, latest_pms_n10p0 = 0;
float latest_wind_speed = NAN;
float latest_ltr390_uvi = NAN, latest_ltr390_lux = NAN;
float latest_gps_quality = NAN, latest_gps_satellites = NAN, latest_gps_accuracy = NAN, latest_gps_altitude = NAN, latest_gps_latitude = NAN, latest_gps_longitude = NAN;

// WiFi and Server config
String wifiSSID = "";
String wifiPassword = "";
String serverURL = "https://techcompanionhk.com/blocky/api/";
String uuidString = "";
bool wifiConnected = false;

// Time Config
String timestamp = "";
long pms_update_time = millis();
long gps_update_time = millis();
long MHZ19B_update_time = millis();
long bme_update_time = millis();
long ltr390_update_time = millis();

bool init_flag = false;

void connectToWiFi() {
  WiFi.disconnect();
  delay(500);
  WiFi.mode(WIFI_STA);
  WiFi.begin(wifiSSID.c_str(), wifiPassword.c_str());
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 10) {
    delay(500);
    attempts++;
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    String ipAddress = WiFi.localIP().toString();
    setInterval(15);
    waitForSync();
    ARDUINO_SERIAL.println("WIFI CONNECT SUCCESS:" + wifiSSID + ":" + ipAddress);
  } else {
    wifiConnected = false;
    String errorCode = String(WiFi.status());
    ARDUINO_SERIAL.println("WIFI CONNECT FAILURE:" + wifiSSID + ":ERROR_" + errorCode);
  }
}

void httpRequest(DynamicJsonDocument doc, String action){
  if (!wifiConnected) {
    ARDUINO_SERIAL.println(action + " FAILURE: WiFi Not Connected");
    return;
  }

  HTTPClient http;
  http.setTimeout(5000); // 10 second timeout
  http.begin(serverURL + "grouped/data"); // Use new grouped endpoint
  http.addHeader("Content-Type", "application/json");

  String jsonString;
  serializeJson(doc, jsonString);
  int httpResponseCode = http.POST(jsonString);
  if (httpResponseCode == 200) {
    ARDUINO_SERIAL.println(action + " SUCCESS: " + String(httpResponseCode));
  } else {
    ARDUINO_SERIAL.println(action + "F AILURE: " + String(httpResponseCode));
  }
  http.end();
}

void uploadAllSensorGroupsToServer() {
  DynamicJsonDocument doc(700); // Optimized: reduced from 1024 to 700 bytes (calculated need: ~650 bytes + 50 byte safety margin)

  // Send data in grouped format for new structured backend
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;

  // BME280 data (prefixed format)
  doc["bme280_temperature"] = latest_bme_temp;
  doc["bme280_humidity"] = latest_bme_hum;
  doc["bme280_pressure"] = latest_bme_pres;

  // PMS7003 data (prefixed format)
  doc["pms7003_pm01"] = latest_pms_pm01;
  doc["pms7003_pm25"] = latest_pms_pm25;
  doc["pms7003_pm10"] = latest_pms_pm10;
  doc["pms7003_n0p3"] = latest_pms_n0p3;
  doc["pms7003_n0p5"] = latest_pms_n0p5;
  doc["pms7003_n1p0"] = latest_pms_n1p0;
  doc["pms7003_n2p5"] = latest_pms_n2p5;
  doc["pms7003_n5p0"] = latest_pms_n5p0;
  doc["pms7003_n10p0"] = latest_pms_n10p0;

  // MH-Z19B data (prefixed format)
  doc["mhz19b_co2"] = latest_mhz19b_co2;
  doc["mhz19b_min_co2"] = latest_mhz19b_minco2;

  // Wind sensor data (prefixed format)
  doc["wind_speed"] = latest_wind_speed;

  // LTR390 UV sensor data (prefixed format)
  doc["ltr390_uvi"] = latest_ltr390_uvi;
  doc["ltr390_lux"] = latest_ltr390_lux;

  // GPS data (prefixed format)
  doc["gps_quality"] = latest_gps_quality;
  doc["gps_satellites"] = latest_gps_satellites;
  doc["gps_accuracy"] = latest_gps_accuracy;
  doc["gps_altitude"] = latest_gps_altitude;
  doc["gps_latitude"] = latest_gps_latitude;
  doc["gps_longitude"] = latest_gps_longitude;
  httpRequest(doc, "UPLOAD");
}

// Upload BME280 sensor group data
void uploadBME280DataToServer() {
  DynamicJsonDocument doc(160); // Optimized: reduced from 256 to 160 bytes (calculated need: ~140 bytes + 20 byte safety margin)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["bme280_temperature"] = latest_bme_temp;
  doc["bme280_humidity"] = latest_bme_hum;
  doc["bme280_pressure"] = latest_bme_pres;

  httpRequest(doc, "UPLOAD");
}

// Upload PMS7003 sensor group data
void uploadPMS7003DataToServer() {
  DynamicJsonDocument doc(320); // Optimized: reduced from 512 to 320 bytes (calculated need: ~280 bytes + 40 byte safety margin)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["pms7003_pm01"] = latest_pms_pm01;
  doc["pms7003_pm25"] = latest_pms_pm25;
  doc["pms7003_pm10"] = latest_pms_pm10;
  doc["pms7003_n0p3"] = latest_pms_n0p3;
  doc["pms7003_n0p5"] = latest_pms_n0p5;
  doc["pms7003_n1p0"] = latest_pms_n1p0;
  doc["pms7003_n2p5"] = latest_pms_n2p5;
  doc["pms7003_n5p0"] = latest_pms_n5p0;
  doc["pms7003_n10p0"] = latest_pms_n10p0;
  httpRequest(doc, "UPLOAD");
}

// Upload MHZ19B sensor group data
void uploadMHZ19BDataToServer() {
  DynamicJsonDocument doc(140); // Optimized: reduced from 256 to 140 bytes (calculated need: ~120 bytes + 20 byte safety margin)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["mhz19b_co2"] = latest_mhz19b_co2;
  doc["mhz19b_min_co2"] = latest_mhz19b_minco2;
  httpRequest(doc, "UPLOAD");
}

// Upload Wind sensor group data
void uploadWindDataToServer() {
  if (!wifiConnected) {
    ARDUINO_SERIAL.println("WIND_UPLOAD_FAILED: WiFi Not Connected");
    return;
  }

  HTTPClient http;
  http.setTimeout(10000);
  http.begin(serverURL + "grouped/data");
  http.addHeader("Content-Type", "application/json");
  DynamicJsonDocument doc(110); // Optimized: reduced from 128 to 110 bytes (calculated need: ~90 bytes + 20 byte safety margin)

  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["wind_speed"] = latest_wind_speed;

  String jsonString;
  serializeJson(doc, jsonString);
  int httpResponseCode = http.POST(jsonString);
  if (httpResponseCode > 0) {
    ARDUINO_SERIAL.println("WIND_UPLOAD_SUCCESS:" + String(httpResponseCode));
  } else {
    ARDUINO_SERIAL.println("WIND_UPLOAD_FAILED:" + String(httpResponseCode));
  }
  http.end();
}

// Upload LTR390 UV sensor group data
void uploadLTR390DataToServer() {
  DynamicJsonDocument doc(130); // Optimized: increased from 128 to 130 bytes (calculated need: ~110 bytes + 20 byte safety margin)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["ltr390_uvi"] = latest_ltr390_uvi;
  doc["ltr390_lux"] = latest_ltr390_lux;
  httpRequest(doc, "UPLOAD");
}

// Upload GPS sensor group data
void uploadGPSDataToServer() {
  DynamicJsonDocument doc(220); // Optimized: reduced from 256 to 220 bytes (calculated need: ~200 bytes + 20 byte safety margin)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["gps_quality"] = latest_gps_quality;
  doc["gps_satellites"] = latest_gps_satellites;
  doc["gps_accuracy"] = latest_gps_accuracy;
  doc["gps_altitude"] = latest_gps_altitude;
  doc["gps_latitude"] = latest_gps_latitude;
  doc["gps_longitude"] = latest_gps_longitude;
  httpRequest(doc, "UPLOAD");
}

// Upload log data to server
void uploadLogDataToServer(String logType, String message) {
  DynamicJsonDocument doc(200); // Optimized: reduced from 512 to 200 bytes (calculated need: ~150 bytes + 50 byte safety margin for variable message length)
  doc["uuid"] = uuidString;
  doc["timestamp"] = timestamp;
  doc["logType"] = logType;
  doc["message"] = message;
  httpRequest(doc, "LOG");
}

void readGpsData() {
  while (GPS_SERIAL.available() > 0) {
    if (gps.read()) {
      latest_gps_quality = gps.Quality;
      latest_gps_satellites = gps.Satellites;
      if (gps.hasFix()) {
        latest_gps_accuracy = gps.Accuracy;
        latest_gps_altitude = gps.Altitude;
        latest_gps_latitude = gps.Latitude;
        latest_gps_longitude = gps.Longitude;
      }
      else {
        latest_gps_accuracy = NAN;
        latest_gps_altitude = NAN;
        latest_gps_latitude = NAN;
        latest_gps_longitude = NAN;
      }
    }
  }
}

void readPmsData() {
  pms.read();
  latest_pms_pm01 = pms.pm01;
  latest_pms_pm25 = pms.pm25;
  latest_pms_pm10 = pms.pm10;
  latest_pms_n0p3 = pms.n0p3;
  latest_pms_n0p5 = pms.n0p5;
  latest_pms_n1p0 = pms.n1p0;
  latest_pms_n2p5 = pms.n2p5;
  latest_pms_n5p0 = pms.n5p0;
  latest_pms_n10p0 = pms.n10p0;
}

void readMHZ19BData() {
  while (MHZ19B_SERIAL.available() > 0) {
    MHZ19B.measure();
    latest_mhz19b_co2 = MHZ19B.getCO2();
    latest_mhz19b_minco2 = MHZ19B.getMinCO2();
    latest_mhz19b_temp = MHZ19B.getTemperature();
    latest_mhz19b_accuracy = MHZ19B.getAccuracy();
  }
}

void readBmeData() {
  bme.read(latest_bme_pres, latest_bme_temp, latest_bme_hum, tempUnit, presUnit);
}

void readLtr390Data() {
  ltr390.setGain(LTR390_GAIN_18);
  ltr390.setResolution(LTR390_RESOLUTION_20BIT);
  ltr390.setMode(LTR390_MODE_UVS);
  latest_ltr390_uvi = ltr390.getUVI();
  ltr390.setGain(LTR390_GAIN_3);
  ltr390.setResolution(LTR390_RESOLUTION_18BIT);
  ltr390.setMode(LTR390_MODE_ALS);
  latest_ltr390_lux = ltr390.getLux();
}

void setup() {
  // UUID
  uint8_t uuid[6];
  esp_efuse_mac_get_default(uuid);
  uuidString = String(uuid[0], HEX) + String(uuid[1], HEX) + String(uuid[2], HEX) + String(uuid[3], HEX) + String(uuid[4], HEX) + String(uuid[5], HEX);

  // Debug Console
  Serial.begin(115200);

  // Arduino Init
  ARDUINO_SERIAL.begin(9600, EspSoftwareSerial::SWSERIAL_8N1, 16, 17);  // Baud Rate, Stop Bit Setting, RX, TX
  ARDUINO_SERIAL.enableIntTx(true);

  // I2C Init
  Wire.begin();   

  // MH-Z19B init
  MHZ19B.begin(&MHZ19B_SERIAL);
  MHZ19B_SERIAL.begin(9600, EspSoftwareSerial::SWSERIAL_8N1, 19, 18);  // Baud Rate, Stop Bit Setting, RX, TX
  MHZ19B_SERIAL.enableIntTx(true);

  // PMS7003 init
  pms.init(); 

  // BME280 init
  bme280_status = bme.begin();

  // LTR390 init
  ltr390_status = ltr390.init();

  // GPS init
  GPS_SERIAL.begin(38400, EspSoftwareSerial::SWSERIAL_8N1, 32, 33);  // Baud Rate, Stop Bit Setting, RX, TX
  GPS_SERIAL.write(dtmDisable, sizeof(dtmDisable));
  GPS_SERIAL.write(gbsDisable, sizeof(gbsDisable));
  GPS_SERIAL.write(gllDisable, sizeof(gllDisable));
  GPS_SERIAL.write(gnsDisable, sizeof(gnsDisable));
  GPS_SERIAL.write(grsDisable, sizeof(grsDisable));
  GPS_SERIAL.write(gsaDisable, sizeof(gsaDisable));
  GPS_SERIAL.write(gstDisable, sizeof(gstDisable));
  GPS_SERIAL.write(gsvDisable, sizeof(gsvDisable));
  GPS_SERIAL.write(rlmDisable, sizeof(rlmDisable));
  GPS_SERIAL.write(thsDisable, sizeof(thsDisable));
  GPS_SERIAL.write(utcDisable, sizeof(utcDisable));
  GPS_SERIAL.write(vlwDisable, sizeof(vlwDisable));
  GPS_SERIAL.write(vtgDisable, sizeof(vtgDisable));
  GPS_SERIAL.write(zdaDisable, sizeof(zdaDisable));

  init_flag = true;
}

void loop() {
  gps_update_time = millis();
  pms_updated = false;
  gps_updated = false;
  MHZ19B_updated = false;
  bme_updated = false;
  ltr390_updated = false;

  // Handle incoming ARDUINO_SERIAL commands
  if (ARDUINO_SERIAL.available() > 0) {
    String cmd = ARDUINO_SERIAL.readStringUntil('\n');
    cmd.trim();
    Serial.println(cmd);
    if (cmd == "R INIT") {
      if (init_flag) {
        ARDUINO_SERIAL.println("YES");
      } else {
        ARDUINO_SERIAL.println("NO");
      }
    } else if (cmd == "R BME T") {
      if (!bme_updated) {
        readBmeData();
        bme_update_time = millis();
        bme_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_bme_temp, 2));
      Serial.println(String(latest_bme_temp, 2));
    } else if (cmd == "R BME H") { 
      if (!bme_updated) {
        readBmeData();
        bme_update_time = millis();
        bme_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_bme_hum, 2));
    } else if (cmd == "R BME P") {
      if (!bme_updated) {
        readBmeData();
        bme_update_time = millis();
        bme_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_bme_pres, 2));
      Serial.println(String(latest_bme_pres, 2));
    } else if (cmd == "R PMS 1.0") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_pm01));
    } else if (cmd == "R PMS 2.5") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_pm25));
      Serial.println(String(latest_pms_pm25));
    } else if (cmd == "R PMS 10.0") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_pm10));
    } else if (cmd == "R PMS N 0.3") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n0p3));
    } else if (cmd == "R PMS N 0.5") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n0p5));
    } else if (cmd == "R PMS N 1.0") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n1p0));
    } else if (cmd == "R PMS N 2.5") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n2p5));
    } else if (cmd == "R PMS N 5.0") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n5p0));
      Serial.println(String(latest_pms_n5p0));
    } else if (cmd == "R PMS N 10.0") {
      if (!pms_updated) {
        readPmsData();
        pms_update_time = millis();
        pms_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_pms_n10p0));
    } else if (cmd == "R MHZ19B CO2") {
      if (!MHZ19B_updated) {
        readMHZ19BData();
        MHZ19B_update_time = millis();
        MHZ19B_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_mhz19b_co2));
      Serial.println(String(latest_mhz19b_co2));
    } else if (cmd == "R MHZ19B Min CO2") {
      if (!MHZ19B_updated) {
        readMHZ19BData();
        MHZ19B_update_time = millis();
        MHZ19B_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_mhz19b_minco2));
    } else if (cmd == "R MHZ19B Temperature") {
      if (!MHZ19B_updated) {
        readMHZ19BData();
        MHZ19B_update_time = millis();
        MHZ19B_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_mhz19b_temp));
    } else if (cmd == "R MHZ19B Accuracy") {
      if (!MHZ19B_updated) {
        readMHZ19BData();
        MHZ19B_update_time = millis();
        MHZ19B_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_mhz19b_accuracy));
    } else if (cmd == "R LTR390 Uvi") {
      if (!ltr390_updated) {
        readLtr390Data();
        ltr390_update_time = millis();
        ltr390_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_ltr390_uvi));
      Serial.println(String(latest_ltr390_uvi));
    } else if (cmd == "R LTR390 Lux") {
      if (!ltr390_updated) {
        readLtr390Data();
        ltr390_update_time = millis();
        ltr390_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_ltr390_lux));
      Serial.println(String(latest_ltr390_lux));
    } else if (cmd == "R GPS Quality") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_quality));
    } else if (cmd == "R GPS Satellites") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_satellites));
      Serial.println(String(latest_gps_satellites));
    } else if (cmd == "R GPS Accuracy") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_accuracy));
    } else if (cmd == "R GPS Altitude") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_altitude));
    } else if (cmd == "R GPS Latitude") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_latitude, 6));
      Serial.println(String(latest_gps_latitude, 6));
    } else if (cmd == "R GPS Longitude") {
      if (!gps_updated) {
        readGpsData();
        gps_update_time = millis();
        gps_updated = true;
      }
      ARDUINO_SERIAL.println(String(latest_gps_longitude, 6));
    } else if (cmd == "U ALL") {
      uploadAllSensorGroupsToServer();
    } else if (cmd == "U BME280") {
      uploadBME280DataToServer();
    } else if (cmd == "U PMS7003") {
      uploadPMS7003DataToServer();
    } else if (cmd == "U MHZ19B") {
      uploadMHZ19BDataToServer();
    } else if (cmd == "U WIND") {
      uploadWindDataToServer();
    } else if (cmd == "U LTR390") {
      uploadLTR390DataToServer();
    } else if (cmd == "U GPS") {
      uploadGPSDataToServer();
    } else if (cmd == "U LOG") {
      String logType = ARDUINO_SERIAL.readStringUntil('\n');
      logType.trim();
      String message = ARDUINO_SERIAL.readStringUntil('\n');
      message.trim();

      // Upload log data to server
      uploadLogDataToServer(logType, message);
    } else if (cmd == "S WIND") {
      String wind_speed = ARDUINO_SERIAL.readStringUntil('\n');
      wind_speed.trim();
      if (wind_speed.toFloat() < 5) {
        latest_wind_speed = wind_speed.toFloat();
      }
      Serial.println(String(latest_wind_speed));
    } else if (cmd == "S WiFi") {
      wifiSSID = ARDUINO_SERIAL.readStringUntil('\n');
      wifiSSID.trim();
      wifiPassword = ARDUINO_SERIAL.readStringUntil('\n');
      wifiPassword.trim();
      connectToWiFi();
    } else if (cmd == "G UUID") {
      ARDUINO_SERIAL.println(uuidString);
    } else if (cmd == "G BME280 STATUS") {
      if (bme280_status) {
        ARDUINO_SERIAL.println("BME280_OK");
      } else {
        ARDUINO_SERIAL.println("BME280_ERROR");
      }
    } else if (cmd == "G PMS7003 STATUS") {
      pms.read();
      if (pms.status == pms.OK) {
        ARDUINO_SERIAL.println("PMS7003_OK");
      } else {
        ARDUINO_SERIAL.println("PMS7003_ERROR");
      }
    } else if (cmd == "G MHZ19B STATUS") {
      if (MHZ19B.measure() == 0) {
        ARDUINO_SERIAL.println("MHZ19B_OK");
      } else {
        ARDUINO_SERIAL.println("MHZ19B_ERROR");
      }
    } else if (cmd == "G LTR390 STATUS") {
      if (ltr390_status) {
        ARDUINO_SERIAL.println("LTR390_OK");
      } else {
        ARDUINO_SERIAL.println("LTR390_ERROR");
      }
    } else if (cmd == "G GPS STATUS") {
      if (gps.isAlive()) {
        ARDUINO_SERIAL.println("GPS_OK");
      } else {
        ARDUINO_SERIAL.println("GPS_ERROR");
      }
    } else if (cmd == "SYNC TIME") {
      if (wifiConnected) {
        timestamp = UTC.dateTime("Y-m-d~TH:i:s.v~Z");
      }
    } else {
      ARDUINO_SERIAL.println("ERR: Unknown Command");
    }
  }
}