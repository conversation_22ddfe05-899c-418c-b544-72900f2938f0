package server

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"blocky-go/internal/config"
	"blocky-go/internal/database"
	"blocky-go/internal/handlers"
)

// Server represents the HTTP server
type Server struct {
	echo   *echo.Echo
	config *config.Config
	logger *Logger
	db     *database.DB
}

// New creates a new server instance
func New(cfg *config.Config, db *database.DB) *Server {
	e := echo.New()
	
	// Hide banner in production
	e.HideBanner = cfg.IsProduction()
	
	// Create logger
	logger := NewLogger(cfg.LogLevel, cfg.LogFormat)
	
	// Set custom error handler
	e.HTTPErrorHandler = HTTPErrorLogger(logger)
	
	// Configure middleware
	SetupMiddleware(e, cfg, logger)
	
	// Setup routes with handlers
	SetupRoutes(e, db)
	
	return &Server{
		echo:   e,
		config: cfg,
		logger: logger,
		db:     db,
	}
}

// Echo returns the underlying echo instance
func (s *Server) Echo() *echo.Echo {
	return s.echo
}

// Start starts the HTTP server with graceful shutdown
func (s *Server) Start() error {
	// Start server in a goroutine
	go func() {
		if err := s.echo.Start(":" + s.config.Port); err != nil && err != http.ErrServerClosed {
			s.echo.Logger.Fatal("Server failed to start:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return s.echo.Shutdown(ctx)
}

// SetupMiddleware configures Echo middleware
func SetupMiddleware(e *echo.Echo, cfg *config.Config, logger *Logger) {
	// Request ID (must be first)
	e.Use(middleware.RequestID())
	
	// Custom request logging
	e.Use(RequestLogger(logger))
	
	// Panic recovery
	e.Use(middleware.Recover())
	
	// CORS
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: cfg.CORSOrigins,
		AllowMethods: []string{echo.GET, echo.POST, echo.PUT, echo.DELETE, echo.OPTIONS},
		AllowHeaders: []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization},
	}))
	
	// Request size limit (10MB)
	e.Use(middleware.BodyLimit("10M"))
	
	// Security headers
	e.Use(middleware.Secure())
	
	// Gzip compression
	e.Use(middleware.Gzip())
}

// SetupRoutes configures all routes
func SetupRoutes(e *echo.Echo, db *database.DB) {
	// Create handlers
	healthHandler := handlers.NewHealthHandler(db)
	infoHandler := handlers.NewInfoHandler()
	
	// Health check endpoint
	e.GET("/health", healthHandler.Check)
	
	// Root endpoint
	e.GET("/", infoHandler.Info)
	
	// Create handlers
	sensorHandler := handlers.NewSensorHandler(db)
	logHandler := handlers.NewLogHandler(db)
	
	// API route group
	api := e.Group("/api")
	
	// Grouped sensor data routes
	grouped := api.Group("/grouped")
	grouped.POST("/data", sensorHandler.PostGroupedData)
	grouped.GET("/latest", sensorHandler.GetLatestData)
	grouped.GET("/devices/summary", sensorHandler.GetDevicesSummary)
	grouped.GET("/devices/basic", sensorHandler.GetDevicesBasicInfo)
	grouped.GET("/devices/:uuid/history", sensorHandler.GetDeviceHistory)
	grouped.DELETE("/data/:uuid", sensorHandler.DeleteDeviceData)
	
	// Log routes
	logs := api.Group("/logs")
	logs.POST("/data", logHandler.PostLogData)
	logs.GET("/device/:uuid", logHandler.GetDeviceLogs)
}