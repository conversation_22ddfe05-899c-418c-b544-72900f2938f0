package services

// SensorConfig defines configuration for a sensor type
type SensorConfig struct {
	Fields  []string          // Expected field names from ESP32
	Table   string            // Database table name
	Mapping map[string]string // Field name mapping (prefixed -> column)
}

// SensorConfigs contains all sensor configurations
var SensorConfigs = map[string]SensorConfig{
	"mhz19b": {
		Fields: []string{"mhz19b_co2", "mhz19b_min_co2", "mhz19b_temperature", "mhz19b_accuracy"},
		Table:  "mhz19b_data",
		Mapping: map[string]string{
			"mhz19b_co2":         "co2",
			"mhz19b_min_co2":     "min_co2",
			"mhz19b_temperature": "temperature",
			"mhz19b_accuracy":    "accuracy",
		},
	},
	"pms7003": {
		Fields: []string{
			"pms7003_pm01", "pms7003_pm25", "pms7003_pm10",
			"pms7003_n0p3", "pms7003_n0p5", "pms7003_n1p0",
			"pms7003_n2p5", "pms7003_n5p0", "pms7003_n10p0",
		},
		Table: "pms7003_data",
		Mapping: map[string]string{
			"pms7003_pm01":  "pm01",
			"pms7003_pm25":  "pm25",
			"pms7003_pm10":  "pm10",
			"pms7003_n0p3":  "n0p3",
			"pms7003_n0p5":  "n0p5",
			"pms7003_n1p0":  "n1p0",
			"pms7003_n2p5":  "n2p5",
			"pms7003_n5p0":  "n5p0",
			"pms7003_n10p0": "n10p0",
		},
	},
	"bme280": {
		Fields: []string{"bme280_temperature", "bme280_humidity", "bme280_pressure"},
		Table:  "bme280_data",
		Mapping: map[string]string{
			"bme280_temperature": "temperature",
			"bme280_humidity":    "humidity",
			"bme280_pressure":    "pressure",
		},
	},
	"wind": {
		Fields: []string{"wind_speed"},
		Table:  "wind_data",
		Mapping: map[string]string{
			"wind_speed": "speed",
		},
	},
	"ltr390": {
		Fields: []string{"ltr390_uvi", "ltr390_lux"},
		Table:  "ltr390_data",
		Mapping: map[string]string{
			"ltr390_uvi": "uvi",
			"ltr390_lux": "lux",
		},
	},
	"gps": {
		Fields: []string{
			"gps_quality", "gps_satellites", "gps_accuracy",
			"gps_altitude", "gps_latitude", "gps_longitude",
		},
		Table: "gps_data",
		Mapping: map[string]string{
			"gps_quality":    "quality",
			"gps_satellites": "satellites",
			"gps_accuracy":   "accuracy",
			"gps_altitude":   "altitude",
			"gps_latitude":   "latitude",
			"gps_longitude":  "longitude",
		},
	},
}

// ExtractSensorFields extracts fields for a specific sensor from the data
func ExtractSensorFields(data map[string]interface{}, fields []string) map[string]interface{} {
	sensorData := make(map[string]interface{})
	
	for _, field := range fields {
		if value, exists := data[field]; exists && value != nil {
			sensorData[field] = value
		}
	}
	
	return sensorData
}

// MapFieldNames maps prefixed field names to database column names
func MapFieldNames(sensorData map[string]interface{}, mapping map[string]string) map[string]interface{} {
	mappedData := make(map[string]interface{})
	
	for prefixedName, value := range sensorData {
		if columnName, exists := mapping[prefixedName]; exists {
			mappedData[columnName] = value
		}
	}
	
	return mappedData
}