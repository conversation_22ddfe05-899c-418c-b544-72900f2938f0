package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"blocky-go/internal/errors"
	"blocky-go/internal/models"
)

// TestSensorService is a simple mock implementation
type TestSensorService struct {
	storeGroupedSensorDataFunc func(ctx context.Context, data map[string]interface{}) error
	getLatestGroupedDataFunc   func(ctx context.Context, uuid string) (map[string]interface{}, error)
	deleteDeviceDataFunc       func(ctx context.Context, uuid string) error
}

func (t *TestSensorService) StoreGroupedSensorData(ctx context.Context, data map[string]interface{}) error {
	if t.storeGroupedSensorDataFunc != nil {
		return t.storeGroupedSensorDataFunc(ctx, data)
	}
	return nil
}

func (t *TestSensorService) GetLatestGroupedData(ctx context.Context, uuid string) (map[string]interface{}, error) {
	if t.getLatestGroupedDataFunc != nil {
		return t.getLatestGroupedDataFunc(ctx, uuid)
	}
	return map[string]interface{}{}, nil
}

func (t *TestSensorService) DeleteDeviceData(ctx context.Context, uuid string) error {
	if t.deleteDeviceDataFunc != nil {
		return t.deleteDeviceDataFunc(ctx, uuid)
	}
	return nil
}

// TestQueries is a simple mock for the queries
type TestQueries struct {
	getDeviceSummaryFunc func(ctx context.Context) ([]models.GetDeviceSummaryRow, error)
	getDeviceUUIDsFunc   func(ctx context.Context) ([]models.GetDeviceUUIDsRow, error)
}

func (t *TestQueries) GetDeviceSummary(ctx context.Context) ([]models.GetDeviceSummaryRow, error) {
	if t.getDeviceSummaryFunc != nil {
		return t.getDeviceSummaryFunc(ctx)
	}
	return []models.GetDeviceSummaryRow{}, nil
}

func (t *TestQueries) GetDeviceUUIDs(ctx context.Context) ([]models.GetDeviceUUIDsRow, error) {
	if t.getDeviceUUIDsFunc != nil {
		return t.getDeviceUUIDsFunc(ctx)
	}
	return []models.GetDeviceUUIDsRow{}, nil
}

func (t *TestQueries) GetBME280History(ctx context.Context, arg models.GetBME280HistoryParams) ([]models.Bme280Data, error) {
	return []models.Bme280Data{}, nil
}

func TestSensorHandler_PostGroupedData(t *testing.T) {
	e := echo.New()
	
	// Custom error handler
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		code := http.StatusInternalServerError
		message := "Internal server error"
		
		// Check if it's an API error
		if apiErr, ok := err.(*errors.APIError); ok {
			code = apiErr.Code
			message = apiErr.Message
		} else if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			if msg, ok := he.Message.(string); ok {
				message = msg
			}
		}
		
		c.JSON(code, map[string]interface{}{
			"error": message,
		})
	}

	t.Run("valid sensor data", func(t *testing.T) {
		called := false
		service := &TestSensorService{
			storeGroupedSensorDataFunc: func(ctx context.Context, data map[string]interface{}) error {
				called = true
				assert.Equal(t, "test-device-123", data["uuid"])
				assert.Equal(t, 25.5, data["bme280_temperature"])
				assert.Equal(t, 60.0, data["bme280_humidity"])
				assert.Equal(t, 1013.25, data["bme280_pressure"])
				return nil
			},
		}
		
		handler := &SensorHandler{service: service, queries: nil}
		
		payload := map[string]interface{}{
			"uuid":               "test-device-123",
			"timestamp":          "2024-01-01T12:00:00Z",
			"bme280_temperature": 25.5,
			"bme280_humidity":    60.0,
			"bme280_pressure":    1013.25,
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/grouped/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostGroupedData(c)
		assert.NoError(t, err)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "success", response["status"])
		assert.Equal(t, "Data stored successfully", response["message"])
	})
	
	t.Run("missing uuid", func(t *testing.T) {
		service := &TestSensorService{}
		handler := &SensorHandler{service: service, queries: nil}
		
		payload := map[string]interface{}{
			"timestamp":          "2024-01-01T12:00:00Z",
			"bme280_temperature": 25.5,
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/grouped/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostGroupedData(c)
		assert.Error(t, err)
		
		// Manually handle error like Echo would
		e.HTTPErrorHandler(err, c)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "Device UUID is required", response["error"])
	})
}

func TestSensorHandler_GetLatestData(t *testing.T) {
	e := echo.New()
	
	t.Run("get latest data success", func(t *testing.T) {
		service := &TestSensorService{
			getLatestGroupedDataFunc: func(ctx context.Context, uuid string) (map[string]interface{}, error) {
				assert.Equal(t, "test-device-123", uuid)
				return map[string]interface{}{
					"uuid":               "test-device-123",
					"timestamp":          time.Now(),
					"bme280_temperature": 25.5,
					"bme280_humidity":    60.0,
				}, nil
			},
		}
		
		handler := &SensorHandler{service: service, queries: nil}
		
		req := httptest.NewRequest(http.MethodGet, "/api/grouped/latest?uuid=test-device-123", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.GetLatestData(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		row := response["row"].(map[string]interface{})
		assert.Equal(t, "test-device-123", row["uuid"])
		assert.Equal(t, 25.5, row["bme280_temperature"])
		assert.Equal(t, 60.0, row["bme280_humidity"])
	})
}

func TestSensorHandler_GetDevicesSummary(t *testing.T) {
	e := echo.New()
	
	t.Run("get devices summary", func(t *testing.T) {
		testTime := time.Now()
		queries := &TestQueries{
			getDeviceSummaryFunc: func(ctx context.Context) ([]models.GetDeviceSummaryRow, error) {
				return []models.GetDeviceSummaryRow{
					{
						DeviceUuid:   "test-device-123",
						TotalRecords: 100,
						FirstSeen:    testTime.Add(-24 * time.Hour),
						LastSeen:     testTime,
						SensorType:   "bme280",
					},
					{
						DeviceUuid:   "test-device-123",
						TotalRecords: 50,
						FirstSeen:    testTime.Add(-20 * time.Hour),
						LastSeen:     testTime.Add(-1 * time.Hour),
						SensorType:   "pms7003",
					},
				}, nil
			},
		}
		
		handler := &SensorHandler{service: nil, queries: queries}
		
		req := httptest.NewRequest(http.MethodGet, "/api/grouped/devices/summary", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.GetDevicesSummary(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response []map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Len(t, response, 1)
		
		device := response[0]
		assert.Equal(t, "test-device-123", device["uuid"])
		assert.Equal(t, float64(150), device["totalRecords"]) // 100 + 50
		
		sensors := device["sensors"].(map[string]interface{})
		assert.Len(t, sensors, 2)
		
		bme280 := sensors["bme280"].(map[string]interface{})
		assert.Equal(t, float64(100), bme280["totalRecords"])
		
		pms7003 := sensors["pms7003"].(map[string]interface{})
		assert.Equal(t, float64(50), pms7003["totalRecords"])
	})
}

func TestSensorHandler_GetDevicesBasicInfo(t *testing.T) {
	e := echo.New()
	
	t.Run("get basic info", func(t *testing.T) {
		testTime := time.Now()
		queries := &TestQueries{
			getDeviceUUIDsFunc: func(ctx context.Context) ([]models.GetDeviceUUIDsRow, error) {
				return []models.GetDeviceUUIDsRow{
					{
						DeviceUuid: "test-device-123",
						LastSeen:   testTime,
					},
					{
						DeviceUuid: "test-device-456",
						LastSeen:   testTime.Add(-1 * time.Hour),
					},
				}, nil
			},
		}
		
		handler := &SensorHandler{service: nil, queries: queries}
		
		req := httptest.NewRequest(http.MethodGet, "/api/grouped/devices/basic", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.GetDevicesBasicInfo(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response []map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Len(t, response, 2)
		
		assert.Equal(t, "test-device-123", response[0]["uuid"])
		assert.Equal(t, "test-device-456", response[1]["uuid"])
	})
}

func TestSensorHandler_DeleteDeviceData(t *testing.T) {
	e := echo.New()
	
	// Custom error handler
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		code := http.StatusInternalServerError
		message := "Internal server error"
		
		// Check if it's an API error
		if apiErr, ok := err.(*errors.APIError); ok {
			code = apiErr.Code
			message = apiErr.Message
		} else if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			if msg, ok := he.Message.(string); ok {
				message = msg
			}
		}
		
		c.JSON(code, map[string]interface{}{
			"error": message,
		})
	}
	
	t.Run("delete success", func(t *testing.T) {
		called := false
		service := &TestSensorService{
			deleteDeviceDataFunc: func(ctx context.Context, uuid string) error {
				called = true
				assert.Equal(t, "test-device-123", uuid)
				return nil
			},
		}
		
		handler := &SensorHandler{service: service, queries: nil}
		
		req := httptest.NewRequest(http.MethodDelete, "/api/grouped/data/test-device-123", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("uuid")
		c.SetParamValues("test-device-123")
		
		err := handler.DeleteDeviceData(c)
		assert.NoError(t, err)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "success", response["status"])
	})
	
	t.Run("missing uuid", func(t *testing.T) {
		service := &TestSensorService{}
		handler := &SensorHandler{service: service, queries: nil}
		
		req := httptest.NewRequest(http.MethodDelete, "/api/grouped/data/", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("uuid")
		c.SetParamValues("")
		
		err := handler.DeleteDeviceData(c)
		assert.Error(t, err)
		
		// Manually handle error like Echo would
		e.HTTPErrorHandler(err, c)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "Device UUID is required", response["error"])
	})
}