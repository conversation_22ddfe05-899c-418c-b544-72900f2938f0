package errors

import (
	"fmt"
	"net/http"
)

// APIError represents a structured API error
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error implements the error interface
func (e *APIError) Error() string {
	return fmt.Sprintf("API Error %d: %s", e.Code, e.Message)
}

// Common errors
var (
	ErrBadRequest          = &APIError{Code: http.StatusBadRequest, Message: "Bad request"}
	ErrNotFound            = &APIError{Code: http.StatusNotFound, Message: "Resource not found"}
	ErrInternalServer      = &APIError{Code: http.StatusInternalServerError, Message: "Internal server error"}
	ErrInvalidUUID         = &APIError{Code: http.StatusBadRequest, Message: "Invalid device UUID"}
	ErrInvalidTimestamp    = &APIError{Code: http.StatusBadRequest, Message: "Invalid timestamp format"}
	ErrMissingDeviceUUID   = &APIError{Code: http.StatusBadRequest, Message: "Device UUID is required"}
	ErrInvalidSensorData   = &APIError{Code: http.StatusBadRequest, Message: "Invalid sensor data format"}
	ErrDatabaseConnection  = &APIError{Code: http.StatusServiceUnavailable, Message: "Database connection error"}
	ErrInvalidPagination   = &APIError{Code: http.StatusBadRequest, Message: "Invalid pagination parameters"}
)

// NewAPIError creates a new API error with custom message
func NewAPIError(code int, message string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
	}
}

// WithDetails adds details to an API error
func (e *APIError) WithDetails(details string) *APIError {
	return &APIError{
		Code:    e.Code,
		Message: e.Message,
		Details: details,
	}
}