.PHONY: help dev build test clean migrate-up migrate-down sqlc-generate setup-dirs

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

dev: ## Run development server with hot reload
	@which air > /dev/null || go install github.com/air-verse/air@latest
	air

build: ## Build the application
	go build -o bin/blocky-go cmd/api/main.go

run: ## Run the built application
	./bin/blocky-go

test: ## Run tests
	go test -v ./...

test-coverage: ## Run tests with coverage
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

clean: ## Clean build artifacts
	rm -rf bin/
	rm -f coverage.out coverage.html

setup-dirs: ## Create project directory structure
	mkdir -p cmd/api
	mkdir -p internal/config
	mkdir -p internal/database/migrations
	mkdir -p internal/handlers
	mkdir -p internal/models
	mkdir -p internal/services
	mkdir -p internal/server
	mkdir -p sql

migrate-up: ## Run database migrations up
	migrate -path internal/database/migrations -database "${DATABASE_URL}" up

migrate-down: ## Run database migrations down
	migrate -path internal/database/migrations -database "${DATABASE_URL}" down

migrate-create: ## Create a new migration (usage: make migrate-create NAME=migration_name)
	migrate create -ext sql -dir internal/database/migrations -seq $(NAME)

sqlc-generate: ## Generate Go code from SQL
	sqlc generate

install-tools: ## Install development tools
	go install github.com/air-verse/air@latest
	go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest
	go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest

deps: ## Download dependencies
	go mod download
	go mod tidy

fmt: ## Format code
	go fmt ./...

lint: ## Run linter
	@which golangci-lint > /dev/null || go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	golangci-lint run

docker-build: ## Build Docker image
	docker build -t blocky-go .

docker-run: ## Run Docker container
	docker run -p 3000:3000 --env-file .env blocky-go