// API configuration for different environments
const API_CONFIG = {
  development: 'http://localhost:8086', // Go backend on Docker
  production: 'http://**********:8086',
  test: 'http://localhost:8087' // Go backend test port
};

// Get the current environment
const environment = import.meta.env.MODE || 'development';

// Allow override via environment variable for testing
const envApiUrl = import.meta.env.VITE_API_URL;

// Export the base API URL
export const API_BASE_URL = envApiUrl || API_CONFIG[environment];

// Helper function to construct full API URLs
export const getApiUrl = (endpoint) => {
  return `${API_BASE_URL}/api${endpoint}`;
}; 