-- Initial schema migration
-- Creates all sensor data tables and indexes

-- BME280 sensor data (temperature, humidity, pressure)
CREATE TABLE IF NOT EXISTS bme280_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    temperature REAL,
    humidity REAL,
    pressure REAL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> logs
CREATE TABLE IF NOT EXISTS device_logs (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    log_type TEXT NOT NULL CHECK (log_type IN ('message', 'warning', 'error')),
    message TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- GPS sensor data
CREATE TABLE IF NOT EXISTS gps_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    latitude REAL,
    longitude REAL,
    altitude REAL,
    quality REAL,
    satellites REAL,
    accuracy REAL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- LTR390 UV sensor data
CREATE TABLE IF NOT EXISTS ltr390_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    uvi REAL,
    lux REAL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- MH-Z19B CO2 sensor data
CREATE TABLE IF NOT EXISTS mhz19b_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    co2 INTEGER,
    min_co2 INTEGER,
    temperature REAL,
    accuracy INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- PMS7003 particulate matter sensor data
CREATE TABLE IF NOT EXISTS pms7003_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    pm01 INTEGER,
    pm25 INTEGER,
    pm10 INTEGER,
    n0p3 INTEGER,
    n0p5 INTEGER,
    n1p0 INTEGER,
    n2p5 INTEGER,
    n5p0 INTEGER,
    n10p0 INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Wind sensor data
CREATE TABLE IF NOT EXISTS wind_data (
    id SERIAL PRIMARY KEY,
    device_uuid TEXT NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    speed REAL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_bme280_device_timestamp ON bme280_data (device_uuid, timestamp DESC);
CREATE INDEX idx_device_logs_device_timestamp ON device_logs (device_uuid, timestamp DESC);
CREATE INDEX idx_gps_device_timestamp ON gps_data (device_uuid, timestamp DESC);
CREATE INDEX idx_ltr390_device_timestamp ON ltr390_data (device_uuid, timestamp DESC);
CREATE INDEX idx_mhz19b_device_timestamp ON mhz19b_data (device_uuid, timestamp DESC);
CREATE INDEX idx_pms7003_device_timestamp ON pms7003_data (device_uuid, timestamp DESC);
CREATE INDEX idx_wind_device_timestamp ON wind_data (device_uuid, timestamp DESC);

-- Additional index for log type filtering
CREATE INDEX idx_device_logs_type ON device_logs (log_type);