package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
	
	"blocky-go/internal/database"
	"blocky-go/internal/errors"
	"blocky-go/internal/models"
	"blocky-go/internal/services"
)

// SensorService interface for dependency injection
type SensorService interface {
	StoreGroupedSensorData(ctx context.Context, data map[string]interface{}) error
	GetLatestGroupedData(ctx context.Context, uuid string) (map[string]interface{}, error)
	DeleteDeviceData(ctx context.Context, uuid string) error
}

// QueriesInterface for dependency injection
type QueriesInterface interface {
	GetDeviceSummary(ctx context.Context) ([]models.GetDeviceSummaryRow, error)
	GetDeviceUUIDs(ctx context.Context) ([]models.GetDeviceUUIDsRow, error)
	GetBME280History(ctx context.Context, arg models.GetBME280HistoryParams) ([]models.Bme280Data, error)
}

// SensorHandler handles sensor data endpoints
type SensorHandler struct {
	service SensorService
	queries QueriesInterface
}

// NewSensorHandler creates a new sensor handler
func NewSensorHandler(db *database.DB) *SensorHandler {
	return &SensorHandler{
		service: services.NewSensorService(db),
		queries: models.New(db.Pool),
	}
}

// PostGroupedData handles POST /api/grouped/data
func (h *SensorHandler) PostGroupedData(c echo.Context) error {
	var data map[string]interface{}
	if err := c.Bind(&data); err != nil {
		return errors.ErrBadRequest.WithDetails("Invalid JSON format")
	}
	
	// Validate UUID
	if _, ok := data["uuid"].(string); !ok {
		return errors.ErrMissingDeviceUUID
	}
	
	// Store the data
	if err := h.service.StoreGroupedSensorData(c.Request().Context(), data); err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	return c.JSON(http.StatusOK, map[string]string{
		"status":  "success",
		"message": "Data stored successfully",
	})
}

// GetLatestData handles GET /api/grouped/latest?uuid=
func (h *SensorHandler) GetLatestData(c echo.Context) error {
	uuid := c.QueryParam("uuid")
	if uuid == "" {
		return errors.ErrMissingDeviceUUID
	}
	
	data, err := h.service.GetLatestGroupedData(c.Request().Context(), uuid)
	if err != nil {
		return errors.ErrNotFound.WithDetails(err.Error())
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"row": data,
	})
}

// GetDevicesSummary handles GET /api/grouped/devices/summary
func (h *SensorHandler) GetDevicesSummary(c echo.Context) error {
	ctx := c.Request().Context()
	
	// Get device summaries from database
	summaries, err := h.queries.GetDeviceSummary(ctx)
	if err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	// Group by device UUID
	deviceMap := make(map[string]map[string]interface{})
	
	for _, summary := range summaries {
		// Type assert the timestamps
		var firstSeenTime, lastSeenTime time.Time
		if ts, ok := summary.FirstSeen.(time.Time); ok {
			firstSeenTime = ts
		}
		if ts, ok := summary.LastSeen.(time.Time); ok {
			lastSeenTime = ts
		}
		
		if _, exists := deviceMap[summary.DeviceUuid]; !exists {
			deviceMap[summary.DeviceUuid] = map[string]interface{}{
				"uuid":        summary.DeviceUuid,
				"lastSeen":    lastSeenTime,
				"firstSeen":   firstSeenTime,
				"totalRecords": 0,
				"sensors":     make(map[string]interface{}),
			}
		}
		
		device := deviceMap[summary.DeviceUuid]
		device["sensors"].(map[string]interface{})[summary.SensorType] = map[string]interface{}{
			"totalRecords": summary.TotalRecords,
			"firstSeen":    firstSeenTime,
			"lastSeen":     lastSeenTime,
		}
		
		// Update overall stats
		totalRecords := device["totalRecords"].(int) + int(summary.TotalRecords)
		device["totalRecords"] = totalRecords
		
		// Update last seen if this sensor is more recent
		if lastSeen, ok := device["lastSeen"].(time.Time); ok {
			if lastSeenTime.After(lastSeen) {
				device["lastSeen"] = lastSeenTime
			}
		}
		
		// Update first seen if this sensor is older
		if firstSeen, ok := device["firstSeen"].(time.Time); ok {
			if firstSeenTime.Before(firstSeen) {
				device["firstSeen"] = firstSeenTime
			}
		}
	}
	
	// Convert map to slice with proper field names to match Node.js
	devices := make([]map[string]interface{}, 0, len(deviceMap))
	for _, device := range deviceMap {
		// Transform field names to match Node.js backend
		formattedDevice := map[string]interface{}{
			"uuid":              device["uuid"],
			"totalRecords":      device["totalRecords"],
			"lastUpdateTime":    device["lastSeen"],    // Node.js uses lastUpdateTime
			"earliestRecord":    device["firstSeen"],   // Node.js uses earliestRecord
			"availableSensors":  []string{},           // Convert sensors map to array
		}
		
		// Extract sensor names from the sensors map
		if sensors, ok := device["sensors"].(map[string]interface{}); ok {
			sensorList := make([]string, 0, len(sensors))
			for sensorName := range sensors {
				sensorList = append(sensorList, sensorName)
			}
			formattedDevice["availableSensors"] = sensorList
		}
		
		devices = append(devices, formattedDevice)
	}
	
	// Return array directly to match frontend expectations
	return c.JSON(http.StatusOK, devices)
}

// GetDevicesBasicInfo handles GET /api/grouped/devices/basic
func (h *SensorHandler) GetDevicesBasicInfo(c echo.Context) error {
	ctx := c.Request().Context()
	
	// Get basic device info
	devices, err := h.queries.GetDeviceUUIDs(ctx)
	if err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	// Return simple array of device UUIDs to match Node.js backend
	result := make([]string, len(devices))
	for i, device := range devices {
		result[i] = device.DeviceUuid
	}
	
	return c.JSON(http.StatusOK, result)
}

// GetDeviceHistory handles GET /api/grouped/devices/:uuid/history
func (h *SensorHandler) GetDeviceHistory(c echo.Context) error {
	uuid := c.Param("uuid")
	if uuid == "" {
		return errors.ErrMissingDeviceUUID
	}
	
	// Parse query parameters
	limit := 100
	offset := 0
	
	if l := c.QueryParam("limit"); l != "" {
		if parsed, err := parseInt(l); err == nil && parsed > 0 && parsed <= 1000 {
			limit = parsed
		}
	}
	
	if o := c.QueryParam("offset"); o != "" {
		if parsed, err := parseInt(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}
	
	// Parse time range
	startTime := time.Now().Add(-24 * time.Hour) // Default to last 24 hours
	endTime := time.Now()
	
	if start := c.QueryParam("start"); start != "" {
		if parsed, err := time.Parse(time.RFC3339, start); err == nil {
			startTime = parsed
		}
	}
	
	if end := c.QueryParam("end"); end != "" {
		if parsed, err := time.Parse(time.RFC3339, end); err == nil {
			endTime = parsed
		}
	}
	
	ctx := c.Request().Context()
	
	// Collect history from all sensor tables
	history := make([]map[string]interface{}, 0)
	
	// BME280 history
	if bme280Data, err := h.queries.GetBME280History(ctx, models.GetBME280HistoryParams{
		DeviceUuid: uuid,
		Timestamp:  pgtype.Timestamp{Time: startTime, Valid: true},
		Timestamp_2: pgtype.Timestamp{Time: endTime, Valid: true},
		Limit:      int32(limit),
		Offset:     int32(offset),
	}); err == nil {
		for _, data := range bme280Data {
			history = append(history, map[string]interface{}{
				"timestamp":          data.Timestamp.Time,
				"sensor":             "bme280",
				"bme280_temperature": data.Temperature,
				"bme280_humidity":    data.Humidity,
				"bme280_pressure":    data.Pressure,
			})
		}
	}
	
	// Sort by timestamp (most recent first)
	// In production, you'd want to do this in the database query
	
	// Return format expected by frontend
	return c.JSON(http.StatusOK, map[string]interface{}{
		"data": history,
		"pagination": map[string]interface{}{
			"total":  len(history), // TODO: This should be total count from DB, not just current page
			"limit":  limit,
			"offset": offset,
		},
	})
}

// DeleteDeviceData handles DELETE /api/grouped/data/:uuid
func (h *SensorHandler) DeleteDeviceData(c echo.Context) error {
	uuid := c.Param("uuid")
	if uuid == "" {
		return errors.ErrMissingDeviceUUID
	}
	
	if err := h.service.DeleteDeviceData(c.Request().Context(), uuid); err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	return c.JSON(http.StatusOK, map[string]string{
		"status": "success",
	})
}

// Helper function to parse int from string
func parseInt(s string) (int, error) {
	var result int
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
}