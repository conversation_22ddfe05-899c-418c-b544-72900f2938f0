package services

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	
	"blocky-go/internal/database"
	"blocky-go/internal/models"
)

// SensorService handles sensor data operations
type SensorService struct {
	db      *database.DB
	queries *models.Queries
}

// NewSensorService creates a new sensor service
func NewSensorService(db *database.DB) *SensorService {
	return &SensorService{
		db:      db,
		queries: models.New(db.Pool),
	}
}

// GroupedDataRequest represents the incoming sensor data from ESP32
type GroupedDataRequest struct {
	UUID      string    `json:"uuid" validate:"required"`
	Timestamp time.Time `json:"timestamp"`
	Data      map[string]interface{}
}

// StoreGroupedSensorData processes and stores grouped sensor data
func (s *SensorService) StoreGroupedSensorData(ctx context.Context, data map[string]interface{}) error {
	// Extract UUID and timestamp
	uuid, ok := data["uuid"].(string)
	if !ok || uuid == "" {
		return fmt.Errorf("device UUID is required")
	}

	// Parse timestamp
	var timestamp time.Time
	if ts, exists := data["timestamp"]; exists {
		switch v := ts.(type) {
		case string:
			parsed, err := time.Parse(time.RFC3339, v)
			if err != nil {
				timestamp = time.Now()
			} else {
				timestamp = parsed
			}
		case float64:
			timestamp = time.Unix(int64(v), 0)
		default:
			timestamp = time.Now()
		}
	} else {
		timestamp = time.Now()
	}

	// Process each sensor group
	for sensorType, config := range SensorConfigs {
		sensorFields := ExtractSensorFields(data, config.Fields)
		
		if len(sensorFields) > 0 {
			// Map field names
			mappedFields := MapFieldNames(sensorFields, config.Mapping)
			
			// Store in appropriate table
			if err := s.storeSensorData(ctx, sensorType, uuid, timestamp, mappedFields); err != nil {
				return fmt.Errorf("failed to store %s data: %w", sensorType, err)
			}
		}
	}

	return nil
}

// storeSensorData stores data for a specific sensor type
func (s *SensorService) storeSensorData(ctx context.Context, sensorType, uuid string, timestamp time.Time, data map[string]interface{}) error {
	switch sensorType {
	case "bme280":
		return s.queries.InsertBME280Data(ctx, models.InsertBME280DataParams{
			DeviceUuid:  uuid,
			Timestamp:   pgtype.Timestamp{Time: timestamp, Valid: true},
			Temperature: getFloat32PG(data["temperature"]),
			Humidity:    getFloat32PG(data["humidity"]),
			Pressure:    getFloat32PG(data["pressure"]),
		})
		
	case "pms7003":
		return s.queries.InsertPMS7003Data(ctx, models.InsertPMS7003DataParams{
			DeviceUuid: uuid,
			Timestamp:  pgtype.Timestamp{Time: timestamp, Valid: true},
			Pm01:       getInt32PG(data["pm01"]),
			Pm25:       getInt32PG(data["pm25"]),
			Pm10:       getInt32PG(data["pm10"]),
			N0p3:       getInt32PG(data["n0p3"]),
			N0p5:       getInt32PG(data["n0p5"]),
			N1p0:       getInt32PG(data["n1p0"]),
			N2p5:       getInt32PG(data["n2p5"]),
			N5p0:       getInt32PG(data["n5p0"]),
			N10p0:      getInt32PG(data["n10p0"]),
		})
		
	case "mhz19b":
		return s.queries.InsertMHZ19BData(ctx, models.InsertMHZ19BDataParams{
			DeviceUuid:  uuid,
			Timestamp:   pgtype.Timestamp{Time: timestamp, Valid: true},
			Co2:         getInt32PG(data["co2"]),
			MinCo2:      getInt32PG(data["min_co2"]),
			Temperature: getFloat32PG(data["temperature"]),
			Accuracy:    getInt32PG(data["accuracy"]),
		})
		
	case "gps":
		return s.queries.InsertGPSData(ctx, models.InsertGPSDataParams{
			DeviceUuid: uuid,
			Timestamp:  pgtype.Timestamp{Time: timestamp, Valid: true},
			Latitude:   getFloat32PG(data["latitude"]),
			Longitude:  getFloat32PG(data["longitude"]),
			Altitude:   getFloat32PG(data["altitude"]),
			Quality:    getFloat32PG(data["quality"]),
			Satellites: getFloat32PG(data["satellites"]),
			Accuracy:   getFloat32PG(data["accuracy"]),
		})
		
	case "ltr390":
		return s.queries.InsertLTR390Data(ctx, models.InsertLTR390DataParams{
			DeviceUuid: uuid,
			Timestamp:  pgtype.Timestamp{Time: timestamp, Valid: true},
			Uvi:        getFloat32PG(data["uvi"]),
			Lux:        getFloat32PG(data["lux"]),
		})
		
	case "wind":
		return s.queries.InsertWindData(ctx, models.InsertWindDataParams{
			DeviceUuid: uuid,
			Timestamp:  pgtype.Timestamp{Time: timestamp, Valid: true},
			Speed:      getFloat32PG(data["speed"]),
		})
		
	default:
		return fmt.Errorf("unknown sensor type: %s", sensorType)
	}
}

// GetLatestGroupedData retrieves the latest sensor data for a device
func (s *SensorService) GetLatestGroupedData(ctx context.Context, uuid string) (map[string]interface{}, error) {
	result := map[string]interface{}{
		"uuid": uuid,
	}
	
	// Get latest data from each sensor table
	if bme280, err := s.queries.GetLatestBME280Data(ctx, uuid); err == nil {
		if bme280.Temperature.Valid {
			result["bme280_temperature"] = bme280.Temperature.Float32
		}
		if bme280.Humidity.Valid {
			result["bme280_humidity"] = bme280.Humidity.Float32
		}
		if bme280.Pressure.Valid {
			result["bme280_pressure"] = bme280.Pressure.Float32
		}
		result["bme280_timestamp"] = bme280.Timestamp.Time
	}
	
	if pms7003, err := s.queries.GetLatestPMS7003Data(ctx, uuid); err == nil {
		if pms7003.Pm01.Valid {
			result["pms7003_pm01"] = pms7003.Pm01.Int32
		}
		if pms7003.Pm25.Valid {
			result["pms7003_pm25"] = pms7003.Pm25.Int32
		}
		if pms7003.Pm10.Valid {
			result["pms7003_pm10"] = pms7003.Pm10.Int32
		}
		if pms7003.N0p3.Valid {
			result["pms7003_n0p3"] = pms7003.N0p3.Int32
		}
		if pms7003.N0p5.Valid {
			result["pms7003_n0p5"] = pms7003.N0p5.Int32
		}
		if pms7003.N1p0.Valid {
			result["pms7003_n1p0"] = pms7003.N1p0.Int32
		}
		if pms7003.N2p5.Valid {
			result["pms7003_n2p5"] = pms7003.N2p5.Int32
		}
		if pms7003.N5p0.Valid {
			result["pms7003_n5p0"] = pms7003.N5p0.Int32
		}
		if pms7003.N10p0.Valid {
			result["pms7003_n10p0"] = pms7003.N10p0.Int32
		}
		result["pms7003_timestamp"] = pms7003.Timestamp.Time
	}
	
	if mhz19b, err := s.queries.GetLatestMHZ19BData(ctx, uuid); err == nil {
		if mhz19b.Co2.Valid {
			result["mhz19b_co2"] = mhz19b.Co2.Int32
		}
		if mhz19b.MinCo2.Valid {
			result["mhz19b_min_co2"] = mhz19b.MinCo2.Int32
		}
		if mhz19b.Temperature.Valid {
			result["mhz19b_temperature"] = mhz19b.Temperature.Float32
		}
		if mhz19b.Accuracy.Valid {
			result["mhz19b_accuracy"] = mhz19b.Accuracy.Int32
		}
		result["mhz19b_timestamp"] = mhz19b.Timestamp.Time
	}
	
	if gps, err := s.queries.GetLatestGPSData(ctx, uuid); err == nil {
		if gps.Latitude.Valid {
			result["gps_latitude"] = gps.Latitude.Float32
		}
		if gps.Longitude.Valid {
			result["gps_longitude"] = gps.Longitude.Float32
		}
		if gps.Altitude.Valid {
			result["gps_altitude"] = gps.Altitude.Float32
		}
		if gps.Quality.Valid {
			result["gps_quality"] = gps.Quality.Float32
		}
		if gps.Satellites.Valid {
			result["gps_satellites"] = gps.Satellites.Float32
		}
		if gps.Accuracy.Valid {
			result["gps_accuracy"] = gps.Accuracy.Float32
		}
		result["gps_timestamp"] = gps.Timestamp.Time
	}
	
	if ltr390, err := s.queries.GetLatestLTR390Data(ctx, uuid); err == nil {
		if ltr390.Uvi.Valid {
			result["ltr390_uvi"] = ltr390.Uvi.Float32
		}
		if ltr390.Lux.Valid {
			result["ltr390_lux"] = ltr390.Lux.Float32
		}
		result["ltr390_timestamp"] = ltr390.Timestamp.Time
	}
	
	if wind, err := s.queries.GetLatestWindData(ctx, uuid); err == nil {
		if wind.Speed.Valid {
			result["wind_speed"] = wind.Speed.Float32
		}
		result["wind_timestamp"] = wind.Timestamp.Time
	}
	
	// Check if we found any data
	if len(result) == 1 {
		return nil, fmt.Errorf("no data found for device %s", uuid)
	}
	
	return result, nil
}

// DeleteDeviceData deletes all data for a device from all sensor tables
func (s *SensorService) DeleteDeviceData(ctx context.Context, uuid string) error {
	// Delete from all sensor tables
	if err := s.queries.DeleteBME280Data(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeletePMS7003Data(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeleteMHZ19BData(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeleteGPSData(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeleteLTR390Data(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeleteWindData(ctx, uuid); err != nil {
		return err
	}
	if err := s.queries.DeleteDeviceLogs(ctx, uuid); err != nil {
		return err
	}
	
	return nil
}

// Helper functions to safely convert interface{} to pgx types
func getFloat32PG(v interface{}) pgtype.Float4 {
	switch val := v.(type) {
	case float64:
		return pgtype.Float4{Float32: float32(val), Valid: true}
	case float32:
		return pgtype.Float4{Float32: val, Valid: true}
	case int:
		return pgtype.Float4{Float32: float32(val), Valid: true}
	case int32:
		return pgtype.Float4{Float32: float32(val), Valid: true}
	case int64:
		return pgtype.Float4{Float32: float32(val), Valid: true}
	default:
		return pgtype.Float4{Valid: false}
	}
}

func getInt32PG(v interface{}) pgtype.Int4 {
	switch val := v.(type) {
	case int:
		return pgtype.Int4{Int32: int32(val), Valid: true}
	case int32:
		return pgtype.Int4{Int32: val, Valid: true}
	case int64:
		return pgtype.Int4{Int32: int32(val), Valid: true}
	case float64:
		return pgtype.Int4{Int32: int32(val), Valid: true}
	case float32:
		return pgtype.Int4{Int32: int32(val), Valid: true}
	default:
		return pgtype.Int4{Valid: false}
	}
}