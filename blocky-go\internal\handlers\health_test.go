package handlers_test

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"blocky-go/internal/handlers"
)

func TestHealthHandler_Check(t *testing.T) {
	// Setup
	e := echo.New()
	handler := handlers.NewHealthHandler(nil) // No database for this test

	// Create request and recorder
	req := httptest.NewRequest(http.MethodGet, "/health", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// Execute
	err := handler.Check(c)
	require.NoError(t, err)

	// Assert response
	assert.Equal(t, http.StatusOK, rec.Code)

	var response handlers.HealthResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "ok", response.Status)
	assert.Equal(t, "1.0.0", response.Version)
	assert.NotZero(t, response.Timestamp)
}

func TestInfoHandler_Info(t *testing.T) {
	// Setup
	e := echo.New()
	handler := handlers.NewInfoHandler()

	// Create request and recorder
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	// Execute
	err := handler.Info(c)
	require.NoError(t, err)

	// Assert response
	assert.Equal(t, http.StatusOK, rec.Code)

	var response handlers.InfoResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "Blocky Go Backend API", response.Message)
	assert.Equal(t, "1.0.0", response.Version)
	assert.NotEmpty(t, response.Uptime)
	assert.Len(t, response.Endpoints, 9)
}