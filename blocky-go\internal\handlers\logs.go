package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	
	"blocky-go/internal/database"
	"blocky-go/internal/errors"
	"blocky-go/internal/models"
	"blocky-go/internal/services"
)

// LogService interface for dependency injection
type LogService interface {
	StoreLogData(ctx context.Context, log services.LogEntry) error
	GetDeviceLogs(ctx context.Context, uuid string, logType *string, startTime, endTime time.Time, limit, offset int) ([]models.DeviceLogs, error)
}

// LogHandler handles log endpoints
type LogHandler struct {
	service LogService
}

// NewLogHandler creates a new log handler
func NewLogHandler(db *database.DB) *LogHandler {
	return &LogHandler{
		service: services.NewLogService(db),
	}
}

// PostLogData handles POST /api/logs/data
func (h *LogHandler) PostLogData(c echo.Context) error {
	var log services.LogEntry
	if err := c.Bind(&log); err != nil {
		return errors.ErrBadRequest.WithDetails("Invalid request body")
	}
	
	// Validate required fields
	if log.DeviceUUID == "" {
		return errors.ErrMissingDeviceUUID
	}
	
	if log.Message == "" {
		return errors.ErrBadRequest.WithDetails("Message is required")
	}
	
	// Default log type to "message" if not provided
	if log.LogType == "" {
		log.LogType = "message"
	}
	
	// Validate log type
	if log.LogType != "message" && log.LogType != "warning" && log.LogType != "error" {
		return errors.ErrBadRequest.WithDetails("Invalid log type")
	}
	
	// Store the log
	if err := h.service.StoreLogData(c.Request().Context(), log); err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	return c.JSON(http.StatusOK, map[string]string{
		"status":  "success",
		"message": "Log stored successfully",
	})
}

// GetDeviceLogs handles GET /api/logs/device/:uuid
func (h *LogHandler) GetDeviceLogs(c echo.Context) error {
	uuid := c.Param("uuid")
	if uuid == "" {
		return errors.ErrMissingDeviceUUID
	}
	
	// Parse query parameters
	limit := 100
	offset := 0
	
	if l := c.QueryParam("limit"); l != "" {
		if parsed, err := parseInt(l); err == nil && parsed > 0 && parsed <= 1000 {
			limit = parsed
		}
	}
	
	if o := c.QueryParam("offset"); o != "" {
		if parsed, err := parseInt(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}
	
	// Parse log type filter
	var logType *string
	if lt := c.QueryParam("logType"); lt != "" {
		if lt == "message" || lt == "warning" || lt == "error" {
			logType = &lt
		}
	}
	
	// Parse time range
	startTime := time.Now().Add(-24 * time.Hour) // Default to last 24 hours
	endTime := time.Now()
	
	if start := c.QueryParam("start"); start != "" {
		if parsed, err := time.Parse(time.RFC3339, start); err == nil {
			startTime = parsed
		}
	}
	
	if end := c.QueryParam("end"); end != "" {
		if parsed, err := time.Parse(time.RFC3339, end); err == nil {
			endTime = parsed
		}
	}
	
	// Get logs from database
	logs, err := h.service.GetDeviceLogs(c.Request().Context(), uuid, logType, startTime, endTime, limit, offset)
	if err != nil {
		return errors.ErrInternalServer.WithDetails(err.Error())
	}
	
	// Format response to match Node.js backend
	response := make([]map[string]interface{}, len(logs))
	for i, log := range logs {
		response[i] = map[string]interface{}{
			"id":          log.ID,
			"device_uuid": uuid,                 // Node.js includes device_uuid
			"timestamp":   log.Timestamp.Time,
			"log_type":    log.LogType,          // Node.js uses log_type not logType
			"message":     log.Message,
		}
	}
	
	// Match Node.js response format
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status": "success",
		"logs":   response,
	})
}