// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package models

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type Bme280Data struct {
	ID          int32            `json:"id"`
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Temperature pgtype.Float4    `json:"temperature"`
	Humidity    pgtype.Float4    `json:"humidity"`
	Pressure    pgtype.Float4    `json:"pressure"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
}

type DeviceLogs struct {
	ID         int32            `json:"id"`
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	LogType    string           `json:"log_type"`
	Message    string           `json:"message"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
}

type GpsData struct {
	ID         int32            `json:"id"`
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Latitude   pgtype.Float4    `json:"latitude"`
	Longitude  pgtype.Float4    `json:"longitude"`
	Altitude   pgtype.Float4    `json:"altitude"`
	Quality    pgtype.Float4    `json:"quality"`
	Satellites pgtype.Float4    `json:"satellites"`
	Accuracy   pgtype.Float4    `json:"accuracy"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
}

type Ltr390Data struct {
	ID         int32            `json:"id"`
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Uvi        pgtype.Float4    `json:"uvi"`
	Lux        pgtype.Float4    `json:"lux"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
}

type Mhz19bData struct {
	ID          int32            `json:"id"`
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Co2         pgtype.Int4      `json:"co2"`
	MinCo2      pgtype.Int4      `json:"min_co2"`
	Temperature pgtype.Float4    `json:"temperature"`
	Accuracy    pgtype.Int4      `json:"accuracy"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
}

type Pms7003Data struct {
	ID         int32            `json:"id"`
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Pm01       pgtype.Int4      `json:"pm01"`
	Pm25       pgtype.Int4      `json:"pm25"`
	Pm10       pgtype.Int4      `json:"pm10"`
	N0p3       pgtype.Int4      `json:"n0p3"`
	N0p5       pgtype.Int4      `json:"n0p5"`
	N1p0       pgtype.Int4      `json:"n1p0"`
	N2p5       pgtype.Int4      `json:"n2p5"`
	N5p0       pgtype.Int4      `json:"n5p0"`
	N10p0      pgtype.Int4      `json:"n10p0"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
}

type WindData struct {
	ID         int32            `json:"id"`
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Speed      pgtype.Float4    `json:"speed"`
	CreatedAt  pgtype.Timestamp `json:"created_at"`
}
