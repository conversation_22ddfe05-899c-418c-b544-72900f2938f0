package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	
	"blocky-go/internal/database"
)

// HealthHandler handles health check requests
type HealthHandler struct {
	db *database.DB
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *database.DB) *HealthHandler {
	return &HealthHandler{db: db}
}

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	Database  string    `json:"database,omitempty"`
}

// Check handles GET /health
func (h *HealthHandler) Check(c echo.Context) error {
	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now().UTC(),
		Version:   "1.0.0",
	}
	
	// Check database connection if available
	if h.db != nil {
		ctx, cancel := context.WithTimeout(c.Request().Context(), 2*time.Second)
		defer cancel()
		
		if err := h.db.Health(ctx); err != nil {
			response.Status = "degraded"
			response.Database = "unhealthy"
			// Still return 200 OK for load balancer compatibility
			return c.JSON(http.StatusOK, response)
		}
		response.Database = "healthy"
	}
	
	return c.JSON(http.StatusOK, response)
}

// InfoHandler handles the root endpoint
type InfoHandler struct {
	startTime time.Time
}

// NewInfoHandler creates a new info handler
func NewInfoHandler() *InfoHandler {
	return &InfoHandler{
		startTime: time.Now(),
	}
}

// InfoResponse represents the API info response
type InfoResponse struct {
	Message   string            `json:"message"`
	Version   string            `json:"version"`
	Uptime    string            `json:"uptime"`
	Endpoints map[string]string `json:"endpoints"`
}

// Info handles GET /
func (h *InfoHandler) Info(c echo.Context) error {
	uptime := time.Since(h.startTime)
	
	response := InfoResponse{
		Message: "Blocky Go Backend API",
		Version: "1.0.0",
		Uptime:  uptime.Round(time.Second).String(),
		Endpoints: map[string]string{
			"health":          "GET /health",
			"grouped_data":    "POST /api/grouped/data",
			"latest_data":     "GET /api/grouped/latest",
			"devices_summary": "GET /api/grouped/devices/summary",
			"devices_basic":   "GET /api/grouped/devices/basic",
			"device_history":  "GET /api/grouped/devices/:uuid/history",
			"delete_device":   "DELETE /api/grouped/data/:uuid",
			"log_data":        "POST /api/logs/data",
			"device_logs":     "GET /api/logs/device/:uuid",
		},
	}
	
	return c.JSON(http.StatusOK, response)
}