# PostgreSQL Configuration (for Docker Compose)
POSTGRES_USER=blocky
POSTGRES_PASSWORD=blocky123
POSTGRES_DB=blocky
POSTGRES_PORT=5432

# API Configuration
API_PORT=8086
CORS_ORIGINS=http://localhost:5173,http://localhost:8085,http://**********:8085
LOG_LEVEL=info

# pgAdmin Configuration (optional - used with --profile tools)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=5050

# Direct Database URL (constructed from above or override for production)
DATABASE_URL=postgres://blocky:blocky123@localhost:5432/blocky?sslmode=disable
PORT=8086

# Environment
ENV=development