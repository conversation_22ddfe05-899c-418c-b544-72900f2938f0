version: '3.8'

services:
  postgres-test:
    image: postgres:16-alpine
    container_name: blocky-postgres-test
    environment:
      POSTGRES_USER: blocky
      POSTGRES_PASSWORD: blocky123
      POSTGRES_DB: blocky_test
    ports:
      - "5433:5432"  # Different port to avoid conflict with existing DB
    volumes:
      - postgres-test-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U blocky -d blocky_test"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres-test-data: