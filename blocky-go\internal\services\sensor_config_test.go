package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMapFieldNames(t *testing.T) {
	t.Run("map BME280 fields", func(t *testing.T) {
		sensorData := map[string]interface{}{
			"bme280_temperature": 25.5,
			"bme280_humidity":    60.0,
			"bme280_pressure":    1013.25,
		}

		mapping := SensorConfigs["bme280"].Mapping
		mapped := MapFieldNames(sensorData, mapping)
		
		assert.Equal(t, 25.5, mapped["temperature"])
		assert.Equal(t, 60.0, mapped["humidity"])
		assert.Equal(t, 1013.25, mapped["pressure"])
	})
	
	t.Run("map PMS7003 fields", func(t *testing.T) {
		sensorData := map[string]interface{}{
			"pms7003_pm01": 10,
			"pms7003_pm25": 20,
			"pms7003_pm10": 30,
		}

		mapping := SensorConfigs["pms7003"].Mapping
		mapped := MapFieldNames(sensorData, mapping)
		
		assert.Equal(t, 10, mapped["pm01"])
		assert.Equal(t, 20, mapped["pm25"])
		assert.Equal(t, 30, mapped["pm10"])
	})

	t.Run("handle fields not in mapping", func(t *testing.T) {
		sensorData := map[string]interface{}{
			"bme280_temperature": 25.5,
			"unknown_field":      "ignored",
			"extra_data":         123,
		}

		mapping := SensorConfigs["bme280"].Mapping
		mapped := MapFieldNames(sensorData, mapping)
		
		assert.Equal(t, 25.5, mapped["temperature"])
		assert.NotContains(t, mapped, "unknown_field")
		assert.NotContains(t, mapped, "extra_data")
		assert.Len(t, mapped, 1) // Only temperature
	})
}

func TestSensorConfigs(t *testing.T) {
	t.Run("verify sensor configs are properly defined", func(t *testing.T) {
		// Check that all expected sensor types exist
		expectedSensors := []string{"bme280", "pms7003", "mhz19b", "gps", "ltr390", "wind"}
		for _, sensor := range expectedSensors {
			cfg, exists := SensorConfigs[sensor]
			assert.True(t, exists, "sensor config for %s should exist", sensor)
			assert.NotEmpty(t, cfg.Table, "sensor %s should have a table", sensor)
			assert.NotEmpty(t, cfg.Fields, "sensor %s should have fields", sensor)
			assert.NotEmpty(t, cfg.Mapping, "sensor %s should have field mappings", sensor)
		}
	})

	t.Run("verify BME280 config", func(t *testing.T) {
		cfg := SensorConfigs["bme280"]
		assert.Equal(t, "bme280_data", cfg.Table)
		assert.Contains(t, cfg.Fields, "bme280_temperature")
		assert.Contains(t, cfg.Fields, "bme280_humidity")
		assert.Contains(t, cfg.Fields, "bme280_pressure")
		assert.Equal(t, "temperature", cfg.Mapping["bme280_temperature"])
		assert.Equal(t, "humidity", cfg.Mapping["bme280_humidity"])
		assert.Equal(t, "pressure", cfg.Mapping["bme280_pressure"])
	})

	t.Run("verify PMS7003 config", func(t *testing.T) {
		cfg := SensorConfigs["pms7003"]
		assert.Equal(t, "pms7003_data", cfg.Table)
		assert.Contains(t, cfg.Fields, "pms7003_pm01")
		assert.Contains(t, cfg.Fields, "pms7003_pm25")
		assert.Contains(t, cfg.Fields, "pms7003_pm10")
		assert.Equal(t, "pm01", cfg.Mapping["pms7003_pm01"])
		assert.Equal(t, "pm25", cfg.Mapping["pms7003_pm25"])
		assert.Equal(t, "pm10", cfg.Mapping["pms7003_pm10"])
	})
}