package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"

	"blocky-go/internal/errors"
	"blocky-go/internal/models"
	"blocky-go/internal/services"
)

// TestLogService is a simple mock implementation
type TestLogService struct {
	storeLogDataFunc func(ctx context.Context, log services.LogEntry) error
	getDeviceLogsFunc func(ctx context.Context, uuid string, logType *string, startTime, endTime time.Time, limit, offset int) ([]models.DeviceLogs, error)
}

func (t *TestLogService) StoreLogData(ctx context.Context, log services.LogEntry) error {
	if t.storeLogDataFunc != nil {
		return t.storeLogDataFunc(ctx, log)
	}
	return nil
}

func (t *TestLogService) GetDeviceLogs(ctx context.Context, uuid string, logType *string, startTime, endTime time.Time, limit, offset int) ([]models.DeviceLogs, error) {
	if t.getDeviceLogsFunc != nil {
		return t.getDeviceLogsFunc(ctx, uuid, logType, startTime, endTime, limit, offset)
	}
	return []models.DeviceLogs{}, nil
}

func TestLogHandler_PostLogData(t *testing.T) {
	e := echo.New()
	
	// Custom error handler that matches production
	e.HTTPErrorHandler = func(err error, c echo.Context) {
		code := http.StatusInternalServerError
		message := "Internal server error"
		
		// Check if it's an API error
		if apiErr, ok := err.(*errors.APIError); ok {
			code = apiErr.Code
			message = apiErr.Message
		} else if he, ok := err.(*echo.HTTPError); ok {
			code = he.Code
			if msg, ok := he.Message.(string); ok {
				message = msg
			}
		}
		
		c.JSON(code, map[string]interface{}{
			"error": message,
		})
	}
	
	t.Run("valid log entry", func(t *testing.T) {
		called := false
		service := &TestLogService{
			storeLogDataFunc: func(ctx context.Context, log services.LogEntry) error {
				called = true
				assert.Equal(t, "test-device-123", log.DeviceUUID)
				assert.Equal(t, "message", log.LogType)
				assert.Equal(t, "Test log message", log.Message)
				return nil
			},
		}
		
		handler := &LogHandler{service: service}
		
		payload := map[string]interface{}{
			"uuid":    "test-device-123",
			"logType": "message",
			"message": "Test log message",
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/logs/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostLogData(c)
		assert.NoError(t, err)
		assert.True(t, called)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "success", response["status"])
		assert.Equal(t, "Log stored successfully", response["message"])
	})
	
	t.Run("missing uuid", func(t *testing.T) {
		service := &TestLogService{}
		handler := &LogHandler{service: service}
		
		payload := map[string]interface{}{
			"logType": "message",
			"message": "Test log message",
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/logs/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostLogData(c)
		assert.Error(t, err)
		
		// Manually handle error like Echo would
		e.HTTPErrorHandler(err, c)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
	})
	
	t.Run("invalid log type", func(t *testing.T) {
		service := &TestLogService{}
		handler := &LogHandler{service: service}
		
		payload := map[string]interface{}{
			"uuid":    "test-device-123",
			"logType": "invalid",
			"message": "Test log message",
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/logs/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostLogData(c)
		assert.Error(t, err)
		
		// Manually handle error like Echo would
		e.HTTPErrorHandler(err, c)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "Bad request", response["error"])
	})
	
	t.Run("missing message", func(t *testing.T) {
		service := &TestLogService{}
		handler := &LogHandler{service: service}
		
		payload := map[string]interface{}{
			"uuid":    "test-device-123",
			"logType": "error",
		}
		
		body, _ := json.Marshal(payload)
		req := httptest.NewRequest(http.MethodPost, "/api/logs/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		err := handler.PostLogData(c)
		assert.Error(t, err)
		
		// Manually handle error like Echo would
		e.HTTPErrorHandler(err, c)
		assert.Equal(t, http.StatusBadRequest, rec.Code)
	})
}

func TestLogHandler_GetDeviceLogs(t *testing.T) {
	e := echo.New()
	
	t.Run("get logs without filters", func(t *testing.T) {
		service := &TestLogService{
			getDeviceLogsFunc: func(ctx context.Context, uuid string, logType *string, startTime, endTime time.Time, limit, offset int) ([]models.DeviceLogs, error) {
				assert.Equal(t, "test-device-123", uuid)
				assert.Nil(t, logType)
				assert.Equal(t, 100, limit)
				assert.Equal(t, 0, offset)
				
				return []models.DeviceLogs{
					{
						ID:         1,
						DeviceUuid: "test-device-123",
						Timestamp:  pgtype.Timestamp{Time: time.Now(), Valid: true},
						LogType:    "message",
						Message:    "Test message",
						CreatedAt:  pgtype.Timestamp{Time: time.Now(), Valid: true},
					},
				}, nil
			},
		}
		
		handler := &LogHandler{service: service}
		
		req := httptest.NewRequest(http.MethodGet, "/api/logs/device/test-device-123", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		c.SetParamNames("uuid")
		c.SetParamValues("test-device-123")
		
		err := handler.GetDeviceLogs(c)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)
		
		var response map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &response)
		assert.Equal(t, "test-device-123", response["uuid"])
		assert.Equal(t, float64(1), response["count"])
		
		logs := response["logs"].([]interface{})
		assert.Len(t, logs, 1)
		
		log := logs[0].(map[string]interface{})
		assert.Equal(t, float64(1), log["id"])
		assert.Equal(t, "message", log["logType"])
		assert.Equal(t, "Test message", log["message"])
	})
}