// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: queries.sql

package models

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const deleteBME280Data = `-- name: DeleteBME280Data :exec
DELETE FROM bme280_data WHERE device_uuid = $1
`

func (q *Queries) DeleteBME280Data(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteBME280Data, deviceUuid)
	return err
}

const deleteDeviceLogs = `-- name: DeleteDeviceLogs :exec
DELETE FROM device_logs WHERE device_uuid = $1
`

func (q *Queries) DeleteDeviceLogs(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteDeviceLogs, deviceUuid)
	return err
}

const deleteGPSData = `-- name: DeleteGPSData :exec
DELETE FROM gps_data WHERE device_uuid = $1
`

func (q *Queries) DeleteGPSData(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteGPSData, deviceUuid)
	return err
}

const deleteLTR390Data = `-- name: DeleteLTR390Data :exec
DELETE FROM ltr390_data WHERE device_uuid = $1
`

func (q *Queries) DeleteLTR390Data(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteLTR390Data, deviceUuid)
	return err
}

const deleteMHZ19BData = `-- name: DeleteMHZ19BData :exec
DELETE FROM mhz19b_data WHERE device_uuid = $1
`

func (q *Queries) DeleteMHZ19BData(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteMHZ19BData, deviceUuid)
	return err
}

const deletePMS7003Data = `-- name: DeletePMS7003Data :exec
DELETE FROM pms7003_data WHERE device_uuid = $1
`

func (q *Queries) DeletePMS7003Data(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deletePMS7003Data, deviceUuid)
	return err
}

const deleteWindData = `-- name: DeleteWindData :exec
DELETE FROM wind_data WHERE device_uuid = $1
`

func (q *Queries) DeleteWindData(ctx context.Context, deviceUuid string) error {
	_, err := q.db.Exec(ctx, deleteWindData, deviceUuid)
	return err
}

const getBME280History = `-- name: GetBME280History :many
SELECT id, device_uuid, timestamp, temperature, humidity, pressure, created_at FROM bme280_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetBME280HistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetBME280History(ctx context.Context, arg GetBME280HistoryParams) ([]Bme280Data, error) {
	rows, err := q.db.Query(ctx, getBME280History,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Bme280Data{}
	for rows.Next() {
		var i Bme280Data
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Temperature,
			&i.Humidity,
			&i.Pressure,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDeviceLogs = `-- name: GetDeviceLogs :many
SELECT id, device_uuid, timestamp, log_type, message, created_at FROM device_logs
WHERE device_uuid = $1
  AND ($2 = '' OR log_type = $2)
  AND timestamp >= $3
  AND timestamp <= $4
ORDER BY timestamp DESC
LIMIT $5 OFFSET $6
`

type GetDeviceLogsParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Column2     interface{}      `json:"column_2"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetDeviceLogs(ctx context.Context, arg GetDeviceLogsParams) ([]DeviceLogs, error) {
	rows, err := q.db.Query(ctx, getDeviceLogs,
		arg.DeviceUuid,
		arg.Column2,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []DeviceLogs{}
	for rows.Next() {
		var i DeviceLogs
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.LogType,
			&i.Message,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDeviceSummary = `-- name: GetDeviceSummary :many
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'bme280' as sensor_type
FROM bme280_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'pms7003' as sensor_type
FROM pms7003_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'mhz19b' as sensor_type
FROM mhz19b_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'gps' as sensor_type
FROM gps_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'ltr390' as sensor_type
FROM ltr390_data
GROUP BY device_uuid
UNION ALL
SELECT 
    device_uuid,
    COUNT(*) as total_records,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    'wind' as sensor_type
FROM wind_data
GROUP BY device_uuid
ORDER BY device_uuid, sensor_type
`

type GetDeviceSummaryRow struct {
	DeviceUuid   string      `json:"device_uuid"`
	TotalRecords int64       `json:"total_records"`
	FirstSeen    interface{} `json:"first_seen"`
	LastSeen     interface{} `json:"last_seen"`
	SensorType   string      `json:"sensor_type"`
}

func (q *Queries) GetDeviceSummary(ctx context.Context) ([]GetDeviceSummaryRow, error) {
	rows, err := q.db.Query(ctx, getDeviceSummary)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetDeviceSummaryRow{}
	for rows.Next() {
		var i GetDeviceSummaryRow
		if err := rows.Scan(
			&i.DeviceUuid,
			&i.TotalRecords,
			&i.FirstSeen,
			&i.LastSeen,
			&i.SensorType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDeviceUUIDs = `-- name: GetDeviceUUIDs :many
SELECT DISTINCT device_uuid, MAX(timestamp) as last_seen
FROM (
    SELECT device_uuid, timestamp FROM bme280_data
    UNION ALL
    SELECT device_uuid, timestamp FROM pms7003_data
    UNION ALL
    SELECT device_uuid, timestamp FROM mhz19b_data
    UNION ALL
    SELECT device_uuid, timestamp FROM gps_data
    UNION ALL
    SELECT device_uuid, timestamp FROM ltr390_data
    UNION ALL
    SELECT device_uuid, timestamp FROM wind_data
) AS all_data
GROUP BY device_uuid
ORDER BY last_seen DESC
`

type GetDeviceUUIDsRow struct {
	DeviceUuid string      `json:"device_uuid"`
	LastSeen   interface{} `json:"last_seen"`
}

func (q *Queries) GetDeviceUUIDs(ctx context.Context) ([]GetDeviceUUIDsRow, error) {
	rows, err := q.db.Query(ctx, getDeviceUUIDs)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetDeviceUUIDsRow{}
	for rows.Next() {
		var i GetDeviceUUIDsRow
		if err := rows.Scan(&i.DeviceUuid, &i.LastSeen); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getGPSHistory = `-- name: GetGPSHistory :many
SELECT id, device_uuid, timestamp, latitude, longitude, altitude, quality, satellites, accuracy, created_at FROM gps_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetGPSHistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetGPSHistory(ctx context.Context, arg GetGPSHistoryParams) ([]GpsData, error) {
	rows, err := q.db.Query(ctx, getGPSHistory,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GpsData{}
	for rows.Next() {
		var i GpsData
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Latitude,
			&i.Longitude,
			&i.Altitude,
			&i.Quality,
			&i.Satellites,
			&i.Accuracy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getLTR390History = `-- name: GetLTR390History :many
SELECT id, device_uuid, timestamp, uvi, lux, created_at FROM ltr390_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetLTR390HistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetLTR390History(ctx context.Context, arg GetLTR390HistoryParams) ([]Ltr390Data, error) {
	rows, err := q.db.Query(ctx, getLTR390History,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Ltr390Data{}
	for rows.Next() {
		var i Ltr390Data
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Uvi,
			&i.Lux,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getLatestBME280Data = `-- name: GetLatestBME280Data :one
SELECT id, device_uuid, timestamp, temperature, humidity, pressure, created_at FROM bme280_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestBME280Data(ctx context.Context, deviceUuid string) (Bme280Data, error) {
	row := q.db.QueryRow(ctx, getLatestBME280Data, deviceUuid)
	var i Bme280Data
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Temperature,
		&i.Humidity,
		&i.Pressure,
		&i.CreatedAt,
	)
	return i, err
}

const getLatestGPSData = `-- name: GetLatestGPSData :one
SELECT id, device_uuid, timestamp, latitude, longitude, altitude, quality, satellites, accuracy, created_at FROM gps_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestGPSData(ctx context.Context, deviceUuid string) (GpsData, error) {
	row := q.db.QueryRow(ctx, getLatestGPSData, deviceUuid)
	var i GpsData
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Latitude,
		&i.Longitude,
		&i.Altitude,
		&i.Quality,
		&i.Satellites,
		&i.Accuracy,
		&i.CreatedAt,
	)
	return i, err
}

const getLatestLTR390Data = `-- name: GetLatestLTR390Data :one
SELECT id, device_uuid, timestamp, uvi, lux, created_at FROM ltr390_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestLTR390Data(ctx context.Context, deviceUuid string) (Ltr390Data, error) {
	row := q.db.QueryRow(ctx, getLatestLTR390Data, deviceUuid)
	var i Ltr390Data
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Uvi,
		&i.Lux,
		&i.CreatedAt,
	)
	return i, err
}

const getLatestMHZ19BData = `-- name: GetLatestMHZ19BData :one
SELECT id, device_uuid, timestamp, co2, min_co2, temperature, accuracy, created_at FROM mhz19b_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestMHZ19BData(ctx context.Context, deviceUuid string) (Mhz19bData, error) {
	row := q.db.QueryRow(ctx, getLatestMHZ19BData, deviceUuid)
	var i Mhz19bData
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Co2,
		&i.MinCo2,
		&i.Temperature,
		&i.Accuracy,
		&i.CreatedAt,
	)
	return i, err
}

const getLatestPMS7003Data = `-- name: GetLatestPMS7003Data :one
SELECT id, device_uuid, timestamp, pm01, pm25, pm10, n0p3, n0p5, n1p0, n2p5, n5p0, n10p0, created_at FROM pms7003_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestPMS7003Data(ctx context.Context, deviceUuid string) (Pms7003Data, error) {
	row := q.db.QueryRow(ctx, getLatestPMS7003Data, deviceUuid)
	var i Pms7003Data
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Pm01,
		&i.Pm25,
		&i.Pm10,
		&i.N0p3,
		&i.N0p5,
		&i.N1p0,
		&i.N2p5,
		&i.N5p0,
		&i.N10p0,
		&i.CreatedAt,
	)
	return i, err
}

const getLatestWindData = `-- name: GetLatestWindData :one
SELECT id, device_uuid, timestamp, speed, created_at FROM wind_data
WHERE device_uuid = $1
ORDER BY timestamp DESC
LIMIT 1
`

func (q *Queries) GetLatestWindData(ctx context.Context, deviceUuid string) (WindData, error) {
	row := q.db.QueryRow(ctx, getLatestWindData, deviceUuid)
	var i WindData
	err := row.Scan(
		&i.ID,
		&i.DeviceUuid,
		&i.Timestamp,
		&i.Speed,
		&i.CreatedAt,
	)
	return i, err
}

const getMHZ19BHistory = `-- name: GetMHZ19BHistory :many
SELECT id, device_uuid, timestamp, co2, min_co2, temperature, accuracy, created_at FROM mhz19b_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetMHZ19BHistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetMHZ19BHistory(ctx context.Context, arg GetMHZ19BHistoryParams) ([]Mhz19bData, error) {
	rows, err := q.db.Query(ctx, getMHZ19BHistory,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Mhz19bData{}
	for rows.Next() {
		var i Mhz19bData
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Co2,
			&i.MinCo2,
			&i.Temperature,
			&i.Accuracy,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPMS7003History = `-- name: GetPMS7003History :many
SELECT id, device_uuid, timestamp, pm01, pm25, pm10, n0p3, n0p5, n1p0, n2p5, n5p0, n10p0, created_at FROM pms7003_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetPMS7003HistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetPMS7003History(ctx context.Context, arg GetPMS7003HistoryParams) ([]Pms7003Data, error) {
	rows, err := q.db.Query(ctx, getPMS7003History,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Pms7003Data{}
	for rows.Next() {
		var i Pms7003Data
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Pm01,
			&i.Pm25,
			&i.Pm10,
			&i.N0p3,
			&i.N0p5,
			&i.N1p0,
			&i.N2p5,
			&i.N5p0,
			&i.N10p0,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getWindHistory = `-- name: GetWindHistory :many
SELECT id, device_uuid, timestamp, speed, created_at FROM wind_data
WHERE device_uuid = $1
  AND timestamp >= $2
  AND timestamp <= $3
ORDER BY timestamp DESC
LIMIT $4 OFFSET $5
`

type GetWindHistoryParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Timestamp_2 pgtype.Timestamp `json:"timestamp_2"`
	Limit       int32            `json:"limit"`
	Offset      int32            `json:"offset"`
}

func (q *Queries) GetWindHistory(ctx context.Context, arg GetWindHistoryParams) ([]WindData, error) {
	rows, err := q.db.Query(ctx, getWindHistory,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Timestamp_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []WindData{}
	for rows.Next() {
		var i WindData
		if err := rows.Scan(
			&i.ID,
			&i.DeviceUuid,
			&i.Timestamp,
			&i.Speed,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const insertBME280Data = `-- name: InsertBME280Data :exec
INSERT INTO bme280_data (device_uuid, timestamp, temperature, humidity, pressure)
VALUES ($1, $2, $3, $4, $5)
`

type InsertBME280DataParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Temperature pgtype.Float4    `json:"temperature"`
	Humidity    pgtype.Float4    `json:"humidity"`
	Pressure    pgtype.Float4    `json:"pressure"`
}

func (q *Queries) InsertBME280Data(ctx context.Context, arg InsertBME280DataParams) error {
	_, err := q.db.Exec(ctx, insertBME280Data,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Temperature,
		arg.Humidity,
		arg.Pressure,
	)
	return err
}

const insertDeviceLog = `-- name: InsertDeviceLog :exec
INSERT INTO device_logs (device_uuid, timestamp, log_type, message)
VALUES ($1, $2, $3, $4)
`

type InsertDeviceLogParams struct {
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	LogType    string           `json:"log_type"`
	Message    string           `json:"message"`
}

func (q *Queries) InsertDeviceLog(ctx context.Context, arg InsertDeviceLogParams) error {
	_, err := q.db.Exec(ctx, insertDeviceLog,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.LogType,
		arg.Message,
	)
	return err
}

const insertGPSData = `-- name: InsertGPSData :exec
INSERT INTO gps_data (device_uuid, timestamp, latitude, longitude, altitude, quality, satellites, accuracy)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
`

type InsertGPSDataParams struct {
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Latitude   pgtype.Float4    `json:"latitude"`
	Longitude  pgtype.Float4    `json:"longitude"`
	Altitude   pgtype.Float4    `json:"altitude"`
	Quality    pgtype.Float4    `json:"quality"`
	Satellites pgtype.Float4    `json:"satellites"`
	Accuracy   pgtype.Float4    `json:"accuracy"`
}

func (q *Queries) InsertGPSData(ctx context.Context, arg InsertGPSDataParams) error {
	_, err := q.db.Exec(ctx, insertGPSData,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Latitude,
		arg.Longitude,
		arg.Altitude,
		arg.Quality,
		arg.Satellites,
		arg.Accuracy,
	)
	return err
}

const insertLTR390Data = `-- name: InsertLTR390Data :exec
INSERT INTO ltr390_data (device_uuid, timestamp, uvi, lux)
VALUES ($1, $2, $3, $4)
`

type InsertLTR390DataParams struct {
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Uvi        pgtype.Float4    `json:"uvi"`
	Lux        pgtype.Float4    `json:"lux"`
}

func (q *Queries) InsertLTR390Data(ctx context.Context, arg InsertLTR390DataParams) error {
	_, err := q.db.Exec(ctx, insertLTR390Data,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Uvi,
		arg.Lux,
	)
	return err
}

const insertMHZ19BData = `-- name: InsertMHZ19BData :exec
INSERT INTO mhz19b_data (device_uuid, timestamp, co2, min_co2, temperature, accuracy)
VALUES ($1, $2, $3, $4, $5, $6)
`

type InsertMHZ19BDataParams struct {
	DeviceUuid  string           `json:"device_uuid"`
	Timestamp   pgtype.Timestamp `json:"timestamp"`
	Co2         pgtype.Int4      `json:"co2"`
	MinCo2      pgtype.Int4      `json:"min_co2"`
	Temperature pgtype.Float4    `json:"temperature"`
	Accuracy    pgtype.Int4      `json:"accuracy"`
}

func (q *Queries) InsertMHZ19BData(ctx context.Context, arg InsertMHZ19BDataParams) error {
	_, err := q.db.Exec(ctx, insertMHZ19BData,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Co2,
		arg.MinCo2,
		arg.Temperature,
		arg.Accuracy,
	)
	return err
}

const insertPMS7003Data = `-- name: InsertPMS7003Data :exec
INSERT INTO pms7003_data (device_uuid, timestamp, pm01, pm25, pm10, n0p3, n0p5, n1p0, n2p5, n5p0, n10p0)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
`

type InsertPMS7003DataParams struct {
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Pm01       pgtype.Int4      `json:"pm01"`
	Pm25       pgtype.Int4      `json:"pm25"`
	Pm10       pgtype.Int4      `json:"pm10"`
	N0p3       pgtype.Int4      `json:"n0p3"`
	N0p5       pgtype.Int4      `json:"n0p5"`
	N1p0       pgtype.Int4      `json:"n1p0"`
	N2p5       pgtype.Int4      `json:"n2p5"`
	N5p0       pgtype.Int4      `json:"n5p0"`
	N10p0      pgtype.Int4      `json:"n10p0"`
}

func (q *Queries) InsertPMS7003Data(ctx context.Context, arg InsertPMS7003DataParams) error {
	_, err := q.db.Exec(ctx, insertPMS7003Data,
		arg.DeviceUuid,
		arg.Timestamp,
		arg.Pm01,
		arg.Pm25,
		arg.Pm10,
		arg.N0p3,
		arg.N0p5,
		arg.N1p0,
		arg.N2p5,
		arg.N5p0,
		arg.N10p0,
	)
	return err
}

const insertWindData = `-- name: InsertWindData :exec
INSERT INTO wind_data (device_uuid, timestamp, speed)
VALUES ($1, $2, $3)
`

type InsertWindDataParams struct {
	DeviceUuid string           `json:"device_uuid"`
	Timestamp  pgtype.Timestamp `json:"timestamp"`
	Speed      pgtype.Float4    `json:"speed"`
}

func (q *Queries) InsertWindData(ctx context.Context, arg InsertWindDataParams) error {
	_, err := q.db.Exec(ctx, insertWindData, arg.DeviceUuid, arg.Timestamp, arg.Speed)
	return err
}
