package server

import (
	"net/http"

	"github.com/labstack/echo/v4"
	
	"blocky-go/internal/errors"
)

// ErrorHandler is a custom error handler for Echo
func ErrorHandler(err error, c echo.Context) {
	// Default to 500 Internal Server Error
	code := http.StatusInternalServerError
	message := "Internal server error"
	details := ""

	// Check if it's an API error
	if apiErr, ok := err.(*errors.APIError); ok {
		code = apiErr.Code
		message = apiErr.Message
		details = apiErr.Details
	} else if httpErr, ok := err.(*echo.HTTPError); ok {
		// Handle Echo HTTP errors
		code = httpErr.Code
		if msg, ok := httpErr.Message.(string); ok {
			message = msg
		}
	}

	// Don't send response if already sent
	if c.Response().Committed {
		return
	}

	// Log error details in development
	if c.Echo().Debug {
		c.Echo().Logger.Error(err)
	}

	// Send JSON error response
	response := map[string]interface{}{
		"error": map[string]interface{}{
			"code":    code,
			"message": message,
		},
	}

	if details != "" {
		response["error"].(map[string]interface{})["details"] = details
	}

	c.JSON(code, response)
}

// ValidateRequestBody validates and binds request body
func ValidateRequestBody(c echo.Context, dst interface{}) error {
	if err := c.Bind(dst); err != nil {
		return errors.ErrBadRequest.WithDetails("Invalid request body format")
	}
	
	if err := c.Validate(dst); err != nil {
		return errors.ErrBadRequest.WithDetails(err.Error())
	}
	
	return nil
}