services:
  postgres:
    image: postgres:16-alpine
    container_name: blocky-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-blocky}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-blocky123}
      POSTGRES_DB: ${POSTGRES_DB:-blocky}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - blocky-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-blocky} -d ${POSTGRES_DB:-blocky}"]
      interval: 10s
      timeout: 5s
      retries: 5

  blocky-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: blocky-api
    restart: unless-stopped
    environment:
      DATABASE_URL: postgres://${POSTGRES_USER:-blocky}:${POSTGRES_PASSWORD:-blocky123}@postgres:5432/${POSTGRES_DB:-blocky}?sslmode=disable
      PORT: ${API_PORT:-8086}
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:5173,http://localhost:8085}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "${API_PORT:-8086}:8086"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - blocky-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8086/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # # Optional: pgAdmin for database management
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: blocky-pgadmin
  #   restart: unless-stopped
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
  #     PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
  #     PGADMIN_CONFIG_SERVER_MODE: 'False'
  #   ports:
  #     - "${PGADMIN_PORT:-5050}:80"
  #   depends_on:
  #     - postgres
  #   networks:
  #     - blocky-network
  #   profiles:
  #     - tools

networks:
  blocky-network:
    driver: bridge

volumes:
  postgres-data:
    driver: local