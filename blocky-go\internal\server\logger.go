package server

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

// Logger provides structured logging
type Logger struct {
	level  string
	format string
}

// LogLevel constants
const (
	LevelDebug = "debug"
	LevelInfo  = "info"
	LevelWarn  = "warn"
	LevelError = "error"
)

// NewLogger creates a new logger instance
func NewLogger(level, format string) *Logger {
	return &Logger{
		level:  strings.ToLower(level),
		format: strings.ToLower(format),
	}
}

// LogEntry represents a structured log entry
type LogEntry struct {
	Timestamp string                 `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// shouldLog checks if a message should be logged based on level
func (l *Logger) shouldLog(level string) bool {
	levels := map[string]int{
		LevelDebug: 0,
		LevelInfo:  1,
		LevelWarn:  2,
		LevelError: 3,
	}
	
	currentLevel, ok1 := levels[l.level]
	messageLevel, ok2 := levels[level]
	
	if !ok1 || !ok2 {
		return true // Log if levels are unknown
	}
	
	return messageLevel >= currentLevel
}

// log writes a log entry
func (l *Logger) log(level, message string, fields map[string]interface{}) {
	if !l.shouldLog(level) {
		return
	}
	
	entry := LogEntry{
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}
	
	if l.format == "json" {
		json.NewEncoder(os.Stdout).Encode(entry)
	} else {
		// Human-readable format
		fmt.Printf("[%s] %s: %s", entry.Timestamp, strings.ToUpper(entry.Level), entry.Message)
		if len(fields) > 0 {
			fmt.Printf(" %v", fields)
		}
		fmt.Println()
	}
}

// Debug logs a debug message
func (l *Logger) Debug(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.log(LevelDebug, message, f)
}

// Info logs an info message
func (l *Logger) Info(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.log(LevelInfo, message, f)
}

// Warn logs a warning message
func (l *Logger) Warn(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.log(LevelWarn, message, f)
}

// Error logs an error message
func (l *Logger) Error(message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	l.log(LevelError, message, f)
}

// RequestLoggerConfig returns Echo middleware config for request logging
func RequestLoggerConfig(logger *Logger) middleware.LoggerConfig {
	return middleware.LoggerConfig{
		Format: "", // We'll use custom format
		Output: &loggerWriter{logger: logger},
		CustomTimeFormat: time.RFC3339,
		Skipper: func(c echo.Context) bool {
			// Skip logging for health checks
			return c.Path() == "/health"
		},
	}
}

// loggerWriter implements io.Writer for Echo logger middleware
type loggerWriter struct {
	logger *Logger
}

func (w *loggerWriter) Write(p []byte) (n int, err error) {
	// Parse the log line and convert to structured format
	// This is a simplified version - in production you'd parse the format string
	w.logger.Info("HTTP Request", map[string]interface{}{
		"data": string(p),
	})
	return len(p), nil
}

// HTTPErrorLogger logs HTTP errors
func HTTPErrorLogger(logger *Logger) echo.HTTPErrorHandler {
	return func(err error, c echo.Context) {
		// Log the error
		logger.Error("HTTP Error", map[string]interface{}{
			"path":   c.Path(),
			"method": c.Request().Method,
			"error":  err.Error(),
			"ip":     c.RealIP(),
		})
		
		// Call the default error handler
		ErrorHandler(err, c)
	}
}

// RequestLogger creates a custom request logging middleware
func RequestLogger(logger *Logger) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()
			
			// Get request ID
			reqID := c.Request().Header.Get(echo.HeaderXRequestID)
			if reqID == "" {
				reqID = c.Response().Header().Get(echo.HeaderXRequestID)
			}
			
			// Process request
			err := next(c)
			
			// Calculate latency
			latency := time.Since(start)
			
			// Log request
			fields := map[string]interface{}{
				"method":     c.Request().Method,
				"path":       c.Path(),
				"status":     c.Response().Status,
				"latency_ms": latency.Milliseconds(),
				"ip":         c.RealIP(),
				"user_agent": c.Request().UserAgent(),
			}
			
			if reqID != "" {
				fields["request_id"] = reqID
			}
			
			if err != nil {
				fields["error"] = err.Error()
				logger.Error("Request failed", fields)
			} else {
				logger.Info("Request completed", fields)
			}
			
			return err
		}
	}
}