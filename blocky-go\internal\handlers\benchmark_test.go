package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/labstack/echo/v4"
	"blocky-go/internal/services"
)

func BenchmarkPostGroupedData(b *testing.B) {
	e := echo.New()
	
	service := &TestSensorService{
		storeGroupedSensorDataFunc: func(ctx context.Context, data map[string]interface{}) error {
			// Simulate minimal processing
			return nil
		},
	}
	
	handler := &SensorHandler{service: service}
	
	payload := map[string]interface{}{
		"uuid":               "test-device-123",
		"timestamp":          time.Now().Format(time.RFC3339),
		"bme280_temperature": 25.5,
		"bme280_humidity":    60.0,
		"bme280_pressure":    1013.25,
		"pms7003_pm1_0":      10,
		"pms7003_pm2_5":      20,
		"pms7003_pm10":       30,
	}
	
	body, _ := json.Marshal(payload)
	
	b.ResetTimer()
	b.<PERSON>()
	
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodPost, "/api/grouped/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler.PostGroupedData(c)
		
		if rec.Code != http.StatusOK {
			b.Fatalf("unexpected status code: %d", rec.Code)
		}
	}
}

func BenchmarkGetLatestData(b *testing.B) {
	e := echo.New()
	
	service := &TestSensorService{
		getLatestGroupedDataFunc: func(ctx context.Context, uuid string) (map[string]interface{}, error) {
			return map[string]interface{}{
				"uuid":               uuid,
				"timestamp":          time.Now(),
				"bme280_temperature": 25.5,
				"bme280_humidity":    60.0,
				"bme280_pressure":    1013.25,
			}, nil
		},
	}
	
	handler := &SensorHandler{service: service}
	
	b.ResetTimer()
	b.ReportAllocs()
	
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/api/grouped/latest?uuid=test-device", nil)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler.GetLatestData(c)
		
		if rec.Code != http.StatusOK {
			b.Fatalf("unexpected status code: %d", rec.Code)
		}
	}
}

func BenchmarkPostLogData(b *testing.B) {
	e := echo.New()
	
	service := &TestLogService{
		storeLogDataFunc: func(ctx context.Context, log services.LogEntry) error {
			return nil
		},
	}
	
	handler := &LogHandler{service: service}
	
	payload := map[string]interface{}{
		"uuid":    "test-device-123",
		"logType": "message",
		"message": "Test log message",
	}
	
	body, _ := json.Marshal(payload)
	
	b.ResetTimer()
	b.ReportAllocs()
	
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodPost, "/api/logs/data", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)
		
		handler.PostLogData(c)
		
		if rec.Code != http.StatusOK {
			b.Fatalf("unexpected status code: %d", rec.Code)
		}
	}
}