-- Rollback initial schema migration
-- Drops all tables and indexes in reverse order

-- Drop indexes first
DROP INDEX IF EXISTS idx_device_logs_type;
DROP INDEX IF EXISTS idx_wind_device_timestamp;
DROP INDEX IF EXISTS idx_pms7003_device_timestamp;
DROP INDEX IF EXISTS idx_mhz19b_device_timestamp;
DROP INDEX IF EXISTS idx_ltr390_device_timestamp;
DROP INDEX IF EXISTS idx_gps_device_timestamp;
DROP INDEX IF EXISTS idx_device_logs_device_timestamp;
DROP INDEX IF EXISTS idx_bme280_device_timestamp;

-- Drop tables
DROP TABLE IF EXISTS wind_data;
DROP TABLE IF EXISTS pms7003_data;
DROP TABLE IF EXISTS mhz19b_data;
DROP TABLE IF EXISTS ltr390_data;
DROP TABLE IF EXISTS gps_data;
DROP TABLE IF EXISTS device_logs;
DROP TABLE IF EXISTS bme280_data;