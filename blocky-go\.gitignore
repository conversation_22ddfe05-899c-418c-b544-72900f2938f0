# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go build command, aka the executable
main

# Go workspace file
go.work

# Dependency directories
vendor/

# Go build cache
.cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Logs
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Hot reload tool
tmp/